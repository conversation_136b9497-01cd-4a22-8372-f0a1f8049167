# GoAssistant Configuration
# Copy this file to .env and update with your actual values

# Application Settings
APP_MODE=development
LOG_LEVEL=info
LOG_FORMAT=json

# Database Configuration
DATABASE_URL=postgres://koopa@localhost:5432/goassistant?sslmode=disable
DB_MAX_CONNECTIONS=25
DB_MIN_CONNECTIONS=5
DB_MAX_IDLE_TIME=15m
DB_MAX_LIFETIME=1h
DB_CONNECT_TIMEOUT=10s
DB_ENABLE_LOGGING=false

# Server Configuration
SERVER_ADDRESS=:8080
SERVER_READ_TIMEOUT=10s
SERVER_WRITE_TIMEOUT=10s
SERVER_IDLE_TIMEOUT=60s
SERVER_SHUTDOWN_TIMEOUT=30s
SERVER_ENABLE_TLS=false
# SERVER_TLS_CERT_FILE=/path/to/cert.pem
# SERVER_TLS_KEY_FILE=/path/to/key.pem

# CLI Configuration
CLI_HISTORY_FILE=.goassistant_history
CLI_MAX_HISTORY_SIZE=1000
CLI_ENABLE_COLORS=true
CLI_PROMPT_TEMPLATE="GoAssistant> "

# AI Provider Configuration (at least one required)
AI_DEFAULT_PROVIDER=claude

# Claude Configuration
CLAUDE_API_KEY=************************************************************************************************************
CLAUDE_MODEL=claude-3-sonnet-20240229
CLAUDE_MAX_TOKENS=4096
CLAUDE_TEMPERATURE=0.7
CLAUDE_BASE_URL=https://api.anthropic.com

# Gemini Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=4096
GEMINI_TEMPERATURE=0.7
GEMINI_BASE_URL=https://generativelanguage.googleapis.com

# Embedding Configuration
EMBEDDING_PROVIDER=claude
EMBEDDING_MODEL=text-embedding-ada-002
EMBEDDING_DIMENSIONS=1536

# Search Tool Configuration
SEARXNG_URL=http://localhost:8888
SEARCH_TIMEOUT=30s
SEARCH_MAX_RESULTS=10
SEARCH_ENABLE_CACHE=true
SEARCH_CACHE_TTL=1h

# PostgreSQL Tool Configuration
POSTGRES_DEFAULT_CONNECTION=postgres://user:password@localhost:5432/mydb
POSTGRES_QUERY_TIMEOUT=30s
POSTGRES_MAX_QUERY_SIZE=1048576
POSTGRES_ENABLE_EXPLAIN=true

# Kubernetes Tool Configuration
KUBECONFIG=/path/to/kubeconfig
KUBE_CONTEXT=default
KUBE_NAMESPACE=default
KUBE_TIMEOUT=30s
KUBE_ENABLE_METRICS=true

# Docker Tool Configuration
DOCKER_HOST=unix:///var/run/docker.sock
DOCKER_API_VERSION=1.41
DOCKER_TIMEOUT=30s
DOCKER_TLS_VERIFY=false

# Cloudflare Tool Configuration
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_API_KEY=your_cloudflare_api_key
CLOUDFLARE_EMAIL=<EMAIL>
CLOUDFLARE_ACCOUNT_ID=your_account_id
CLOUDFLARE_ZONE_ID=your_zone_id

# LangChain Tool Configuration
LANGCHAIN_ENABLE_MEMORY=true
LANGCHAIN_MEMORY_SIZE=10
LANGCHAIN_MAX_ITERATIONS=5
LANGCHAIN_TIMEOUT=60s

# Security Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRATION=24h
RATE_LIMIT_RPS=100
RATE_LIMIT_BURST=200
ENABLE_CORS=true
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Development Configuration
CONFIG_FILE=configs/development.yaml
CONFIG_DIR=configs
