# GoAssistant UI Design Guidelines

## Overview

This document provides comprehensive UI/UX design guidelines for GoAssistant, utilizing Material Design 3 principles with a blue and white color theme, implemented using Go + HTMX + Templ, with full internationalization support for English and Traditional Chinese (繁體中文).

## Design System

### Core Principles

1. **Material Design 3 (Material You)**
  - Dynamic color system with blue and white theme
  - User personalization for theme preferences
  - Emphasis on large, bold typography
  - Rounded corners and soft edges (border-radius: 16px-24px)
  - Elevated surfaces with subtle shadows in light mode
  - Tinted surfaces for elevation in dark mode
  - Adaptive layouts for different screen sizes

2. **Tailwind CSS Integration**
  - Utility-first approach for rapid development
  - Custom color palette based on MD3 tokens
  - Responsive design with mobile-first approach
  - Component variants using Tailwind modifiers

3. **HTMX Philosophy**
  - Progressive enhancement
  - Server-side rendering with dynamic updates
  - Minimal JavaScript for maximum performance
  - Smooth transitions and loading states

### Color System Quick Reference

| Element | Light Mode | Dark Mode | Usage |
|---------|------------|-----------|--------|
| **Primary** | #0061A4 (<PERSON> Blue) | #9ECAFF (Light Blue) | Buttons, links, focus states |
| **Background** | #FDFCFF (Pure White) | #1A1C1E (Dark) | Page background |
| **Surface** | #FDFCFF (White) | #1A1C1E (Dark) | Cards, dialogs |
| **Primary Container** | #D1E4FF (Light Blue) | #00497D (Dark Blue) | Selected states, badges |
| **Secondary** | #535F70 (Blue Grey) | #BBD9F0 (Light Blue Grey) | Secondary actions |
| **Error** | #BA1A1A (Red) | #FFB4AB (Light Red) | Error states |
| **Outline** | #73777F (Grey) | #8D9199 (Light Grey) | Borders, dividers |

### Color System

```css
/* Material Design 3 Color Tokens - Blue & White Theme */
/* Light Mode */
--md-sys-color-primary: #0061A4;           /* Rich Blue */
--md-sys-color-on-primary: #FFFFFF;        /* White on Primary */
--md-sys-color-primary-container: #D1E4FF; /* Light Blue Container */
--md-sys-color-on-primary-container: #001D36; /* Dark Blue on Container */

--md-sys-color-secondary: #535F70;         /* Blue Grey */
--md-sys-color-on-secondary: #FFFFFF;
--md-sys-color-secondary-container: #D7E3F7; /* Light Blue Grey */
--md-sys-color-on-secondary-container: #101C2B;

--md-sys-color-tertiary: #6B5D00;          /* Accent Gold */
--md-sys-color-on-tertiary: #FFFFFF;
--md-sys-color-tertiary-container: #F7E087;
--md-sys-color-on-tertiary-container: #201C00;

--md-sys-color-error: #BA1A1A;
--md-sys-color-on-error: #FFFFFF;
--md-sys-color-error-container: #FFDAD6;
--md-sys-color-on-error-container: #410002;

--md-sys-color-background: #FDFCFF;        /* Pure White Background */
--md-sys-color-on-background: #1A1C1E;
--md-sys-color-surface: #FDFCFF;           /* White Surface */
--md-sys-color-on-surface: #1A1C1E;
--md-sys-color-surface-variant: #DFE2EB;   /* Light Grey */
--md-sys-color-on-surface-variant: #43474E;
--md-sys-color-outline: #73777F;
--md-sys-color-outline-variant: #C3C7CF;

/* Dark Mode */
--md-sys-color-primary-dark: #9ECAFF;      /* Light Blue */
--md-sys-color-on-primary-dark: #003258;
--md-sys-color-primary-container-dark: #00497D;
--md-sys-color-on-primary-container-dark: #D1E4FF;

--md-sys-color-secondary-dark: #BBD9F0;
--md-sys-color-on-secondary-dark: #253140;
--md-sys-color-secondary-container-dark: #3C4857;
--md-sys-color-on-secondary-container-dark: #D7E3F7;

--md-sys-color-tertiary-dark: #DBC961;
--md-sys-color-on-tertiary-dark: #373100;
--md-sys-color-tertiary-container-dark: #504600;
--md-sys-color-on-tertiary-container-dark: #F7E087;

--md-sys-color-error-dark: #FFB4AB;
--md-sys-color-on-error-dark: #690005;
--md-sys-color-error-container-dark: #93000A;
--md-sys-color-on-error-container-dark: #FFDAD6;

--md-sys-color-background-dark: #1A1C1E;   /* Dark Background */
--md-sys-color-on-background-dark: #E2E2E6;
--md-sys-color-surface-dark: #1A1C1E;      /* Dark Surface */
--md-sys-color-on-surface-dark: #E2E2E6;
--md-sys-color-surface-variant-dark: #43474E;
--md-sys-color-on-surface-variant-dark: #C3C7CF;
--md-sys-color-outline-dark: #8D9199;
--md-sys-color-outline-variant-dark: #43474E;

/* Elevation Tints for Dark Mode */
--md-sys-color-surface-tint-dark: #9ECAFF;
--md-sys-elevation-level1-dark: #222529;
--md-sys-elevation-level2-dark: #272A2E;
--md-sys-elevation-level3-dark: #2C2F33;
--md-sys-elevation-level4-dark: #2E3135;
--md-sys-elevation-level5-dark: #313539;
```

### Theme Implementation

```css
/* CSS Variables for Theme Switching */
:root {
  /* Default to light theme */
  --primary: var(--md-sys-color-primary);
  --on-primary: var(--md-sys-color-on-primary);
  --primary-container: var(--md-sys-color-primary-container);
  --on-primary-container: var(--md-sys-color-on-primary-container);
  /* ... other color mappings */
}

[data-theme="dark"] {
  --primary: var(--md-sys-color-primary-dark);
  --on-primary: var(--md-sys-color-on-primary-dark);
  --primary-container: var(--md-sys-color-primary-container-dark);
  --on-primary-container: var(--md-sys-color-on-primary-container-dark);
  /* ... other dark mode mappings */
}

/* Tailwind Config Extension */
module.exports = {
  theme: {
    extend: {
      colors: {
        'primary': 'var(--primary)',
        'on-primary': 'var(--on-primary)',
        'primary-container': 'var(--primary-container)',
        'on-primary-container': 'var(--on-primary-container)',
        /* ... other color mappings */
      }
    }
  }
}
```

### Typography

- **Display**: 57px - Page titles
- **Headline Large**: 32px - Section headers
- **Headline Medium**: 28px - Card titles
- **Title Large**: 22px - Subsection headers
- **Body Large**: 16px - Main content
- **Label Large**: 14px - Buttons and inputs

**Font Stack**:
```css
/* Latin */
--font-family-base: 'Roboto', 'Helvetica Neue', Arial, sans-serif;

/* Traditional Chinese - Using Noto Sans TC for better readability */
--font-family-zh-tw: 'Noto Sans TC', 'Microsoft JhengHei', 'PingFang TC', sans-serif;

/* Combined Font Stack */
body {
  font-family: var(--font-family-base), var(--font-family-zh-tw);
}
```

## Internationalization (i18n)

### Language Support
The application supports:
- **English (en-US)** - Default language
- **Traditional Chinese (zh-TW)** - 繁體中文

### Implementation Strategy

#### 1. Language Detection
```html
<!-- Language Selector in Navigation -->
<div class="relative" x-data="{ open: false }">
    <button @click="open = !open" 
            class="flex items-center gap-2 px-4 py-2 rounded-full
                   hover:bg-surface-variant transition-colors">
        <svg class="w-5 h-5"><!-- Globe Icon --></svg>
        <span>{{ .CurrentLang }}</span>
    </button>
    <div x-show="open" @click.away="open = false"
         class="absolute right-0 mt-2 w-48 bg-surface rounded-2xl
                shadow-lg border border-outline overflow-hidden">
        <a href="?lang=en" class="block px-4 py-3 hover:bg-surface-variant
                                  {{ if eq .Lang "en" }}bg-primary-container{{ end }}">
            English
        </a>
        <a href="?lang=zh-TW" class="block px-4 py-3 hover:bg-surface-variant
                                     {{ if eq .Lang "zh-TW" }}bg-primary-container{{ end }}">
            繁體中文
        </a>
    </div>
</div>
```

#### 2. Translation Structure
```go
// internal/i18n/translations.go
var translations = map[string]map[string]string{
    "en": {
        // Navigation
        "nav.home": "Home",
        "nav.chat": "Chat",
        "nav.tools": "Tools",
        "nav.development": "Development",
        "nav.database": "Database",
        "nav.infrastructure": "Infrastructure",
        "nav.settings": "Settings",
        
        // Dashboard
        "dashboard.welcome": "Welcome to GoAssistant",
        "dashboard.quick_stats": "Quick Stats",
        "dashboard.recent_chats": "Recent Conversations",
        "dashboard.active_agents": "Active Agents",
        
        // Chat Interface
        "chat.new_conversation": "New Conversation",
        "chat.type_message": "Type your message...",
        "chat.send": "Send",
        "chat.thinking": "Thinking...",
        "chat.error": "An error occurred",
        
        // Common Actions
        "action.save": "Save",
        "action.cancel": "Cancel",
        "action.delete": "Delete",
        "action.edit": "Edit",
        "action.search": "Search",
        "action.filter": "Filter",
        "action.export": "Export",
        "action.import": "Import",
    },
    "zh-TW": {
        // Navigation
        "nav.home": "首頁",
        "nav.chat": "對話",
        "nav.tools": "工具",
        "nav.development": "開發助手",
        "nav.database": "資料庫",
        "nav.infrastructure": "基礎設施",
        "nav.settings": "設定",
        
        // Dashboard
        "dashboard.welcome": "歡迎使用 GoAssistant",
        "dashboard.quick_stats": "快速統計",
        "dashboard.recent_chats": "最近對話",
        "dashboard.active_agents": "活躍代理",
        
        // Chat Interface
        "chat.new_conversation": "新對話",
        "chat.type_message": "輸入您的訊息...",
        "chat.send": "傳送",
        "chat.thinking": "思考中...",
        "chat.error": "發生錯誤",
        
        // Common Actions
        "action.save": "儲存",
        "action.cancel": "取消",
        "action.delete": "刪除",
        "action.edit": "編輯",
        "action.search": "搜尋",
        "action.filter": "篩選",
        "action.export": "匯出",
        "action.import": "匯入",
    },
}
```

#### 3. Templ Integration
```html
<!-- Using translations in Templ -->
{{ templ DashboardPage(lang string) }}
<div class="container mx-auto p-6">
    <h1 class="text-display mb-8">{{ t "dashboard.welcome" lang }}</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-surface rounded-3xl p-6 shadow-md">
            <h3 class="text-title-large mb-2">{{ t "dashboard.quick_stats" lang }}</h3>
            <!-- Stats content -->
        </div>
    </div>
</div>
{{ end }}
```

#### 4. Language-Specific Formatting
```go
// Date/Time Formatting
func FormatDateTime(t time.Time, lang string) string {
    switch lang {
    case "zh-TW":
        return t.Format("2006年1月2日 15:04")
    default:
        return t.Format("Jan 2, 2006 3:04 PM")
    }
}

// Number Formatting
func FormatNumber(n int, lang string) string {
    switch lang {
    case "zh-TW":
        // Chinese number formatting
        return formatChineseNumber(n)
    default:
        // English number formatting with commas
        return humanize.Comma(int64(n))
    }
}
```

### Text Direction and Layout
Both English and Traditional Chinese use left-to-right (LTR) text direction, so no special RTL handling is needed.

### Language-Specific UI Considerations

#### Traditional Chinese (zh-TW)
- Allow more space for text as Chinese characters may require different spacing
- Ensure font size is adequate for character complexity
- Test line-height for better readability

#### Typography Adjustments
```css
/* Language-specific typography */
[lang="zh-TW"] {
  /* Slightly larger font size for better readability */
  --body-font-size: 17px;
  --label-font-size: 15px;
  
  /* Adjusted line height */
  --line-height-base: 1.7;
}

[lang="en"] {
  --body-font-size: 16px;
  --label-font-size: 14px;
  --line-height-base: 1.5;
}
```

## Page Structure

### 1. Dashboard (Home)

**Purpose**: Central hub for all assistant capabilities with personalized experience

**Layout**:
```
┌─────────────────────────────────────────┐
│  Navigation Bar with Lang Selector      │
├─────────────────────────────────────────┤
│  Welcome Section                        │
│  ┌─────────────┐ ┌─────────────┐       │
│  │ Quick Stats │ │ Recent Chats│       │
│  └─────────────┘ └─────────────┘       │
├─────────────────────────────────────────┤
│  Agent Cards Grid                       │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐     │
│  │ Dev │ │ DB  │ │Infra│ │ AI  │     │
│  └─────┘ └─────┘ └─────┘ └─────┘     │
├─────────────────────────────────────────┤
│  Recent Activities                      │
└─────────────────────────────────────────┘
```

**Components**:
- **Navigation Bar**:
  - Logo with blue accent
  - Search bar with white background
  - Language selector (EN/繁體中文)
  - Theme toggle (light/dark)
  - User menu
- **Welcome Section**:
  - Personalized greeting in selected language
  - Dynamic time-based messages (早安/Good morning)
  - Context-aware suggestions
- **Quick Stats**:
  - White cards with blue accent borders
  - Real-time metrics with localized number formatting
  - Animated counters
- **Agent Cards**:
  - Blue gradient backgrounds
  - White text and icons
  - Hover effects with elevation
  - Status indicators in corner
- **Recent Activities**:
  - Timeline with blue accent lines
  - Localized timestamps
  - Activity type icons

**HTMX Features**:
- Live stats updates via SSE
- Lazy-loaded activity feed with `hx-trigger="intersect"`
- Click-to-expand agent details
- Language switching without page reload

### 2. Chat Interface

**Purpose**: Primary interaction with AI assistants

**Layout**:
```
┌─────────────────────────────────────────┐
│  Chat Header (Agent Name, Status, Lang) │
├────────┬────────────────────────────────┤
│        │  Messages Area                 │
│ Side   │  ┌──────────────────────┐     │
│ Panel  │  │ Assistant Message    │     │
│        │  └──────────────────────┘     │
│ Tools  │         ┌──────────────┐      │
│ Memory │         │ User Message │      │
│ Files  │         └──────────────┘      │
│        ├────────────────────────────────┤
│        │  Input Area + Send Button      │
└────────┴────────────────────────────────┘
```

**Components**:
- **Chat Header**:
  - Current agent name with blue accent
  - Online/processing status indicator
  - Conversation actions (new, export, share)
  - Language quick toggle
- **Side Panel**:
  - White background with blue accents
  - Active tools with status badges
  - Memory context viewer
  - File attachments with drag-drop
  - Conversation history list
- **Messages Area**:
  - User messages: Blue background, white text
  - Assistant messages: White background with blue border
  - Code blocks with syntax highlighting
  - Tool execution cards with progress
  - Loading dots animation
  - Timestamp in selected language format
- **Input Area**:
  - White textarea with blue focus border
  - Multi-language input support
  - File upload with preview
  - Voice input toggle (if supported)
  - Send button with loading state

**HTMX Features**:
- WebSocket for real-time streaming
- Message updates without page refresh
- Infinite scroll for history
- Live typing indicators
- Auto-save draft messages

### 3. Tools Dashboard

**Purpose**: Manage and monitor integrated tools

**Layout**:
```
┌─────────────────────────────────────────┐
│  Tools Overview                         │
├─────────────────────────────────────────┤
│  Search/Filter Bar                      │
├─────────────────────────────────────────┤
│  Tools Grid                             │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐│
│  │ Go Dev   │ │PostgreSQL│ │   K8s   ││
│  │ ▣ Active │ │ ▣ Active │ │ ○ Idle  ││
│  └──────────┘ └──────────┘ └──────────┘│
│  ┌──────────┐ ┌──────────┐ ┌──────────┐│
│  │  Docker  │ │Cloudflare│ │  Search ││
│  │ ▣ Active │ │ ○ Idle  │ │ ▣ Active││
│  └──────────┘ └──────────┘ └──────────┘│
└─────────────────────────────────────────┘
```

**Components**:
- **Tool Cards**:
  - Status indicator (active/idle/error)
  - Recent usage statistics
  - Quick actions (configure, logs, disable)
  - Performance metrics
- **Detail Modal** (on click):
  - Configuration options
  - Usage history graph
  - Error logs
  - Test interface

**HTMX Features**:
- Real-time status updates
- Click-to-configure without page reload
- Live metrics updates

### 4. Development Assistant

**Purpose**: Specialized interface for Go development features

**Layout**:
```
┌─────────────────────────────────────────┐
│  Go Development Assistant               │
├──────────┬──────────────────────────────┤
│          │  Code Editor/Viewer          │
│ Feature  │  ┌────────────────────┐     │
│ Sidebar  │  │ package main      │     │
│          │  │ import "fmt"      │     │
│ □ AST    │  └────────────────────┘     │
│ □ pprof  ├──────────────────────────────┤
│ □ Trace  │  Analysis Results            │
│ □ Debug  │  ┌────────────────────┐     │
│          │  │ Performance Graph │     │
│          │  └────────────────────┘     │
└──────────┴──────────────────────────────┘
```

**Components**:
- **Feature Sidebar**: Toggle different analysis tools
- **Code Editor**:
  - Syntax highlighting
  - Line numbers
  - Error indicators
  - Quick fixes
- **Results Panel**:
  - AST visualization
  - Performance flamegraphs
  - Trace timeline
  - Memory allocation charts

**HTMX Features**:
- File upload for analysis
- Real-time profiling updates
- Interactive graphs

### 5. Database Manager

**Purpose**: PostgreSQL management and optimization

**Layout**:
```
┌─────────────────────────────────────────┐
│  Database Manager                       │
├──────────┬──────────────────────────────┤
│ Schema   │  Query Editor               │
│ Explorer │  ┌────────────────────┐     │
│          │  │ SELECT * FROM ... │     │
│ ▼ Tables │  └────────────────────┘     │
│   users  ├──────────────────────────────┤
│   posts  │  Results Table              │
│ ▼ Views  │  ┌────────────────────┐     │
│          │  │ id │ name │ email │     │
│          │  └────────────────────┘     │
└──────────┴──────────────────────────────┘
```

**Components**:
- **Schema Explorer**: Tree view of database objects
- **Query Editor**:
  - SQL syntax highlighting
  - Auto-completion
  - Query history
- **Results Viewer**:
  - Tabular data display
  - Export options
  - Pagination
- **Query Analyzer**:
  - EXPLAIN visualization
  - Performance suggestions
  - Index recommendations

### 6. Infrastructure Monitor

**Purpose**: Kubernetes and Docker management

**Layout**:
```
┌─────────────────────────────────────────┐
│  Infrastructure Monitor                 │
├─────────────────────────────────────────┤
│  Cluster Overview                       │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐  │
│  │ Nodes:3 │ │ Pods:24 │ │ CPU:45% │  │
│  └─────────┘ └─────────┘ └─────────┘  │
├─────────────────────────────────────────┤
│  Resources List                        │
│  ┌─────────────────────────────────┐  │
│  │ Deployments │ Pods │ Services  │  │
│  └─────────────────────────────────┘  │
│  Resource details table...             │
└─────────────────────────────────────────┘
```

### 7. Settings

**Purpose**: Application configuration and preferences

**Sections**:
- **General Settings**
  - Language preference (English/繁體中文)
  - Theme selection (Light/Dark/System)
  - Timezone settings
  - Date/Time format preferences
- **AI Provider Configuration**
  - Claude API settings
  - Gemini API settings
  - Model selection
  - Temperature and parameter tuning
- **Tool Settings**
  - Individual tool enable/disable
  - Tool-specific configurations
  - Connection testing
- **Appearance**
  - Font size adjustments
  - Color accent customization
  - Compact/Comfortable/Spacious density
- **API Keys Management**
  - Secure key storage
  - Key rotation reminders
  - Usage statistics
- **Database Connections**
  - PostgreSQL connection strings
  - Connection pool settings
  - Backup configurations
- **Notification Preferences**
  - Email notifications
  - In-app notifications
  - Alert thresholds
- **Privacy & Security**
  - Data retention policies
  - Export personal data
  - Delete account option

## Component Library

### Buttons

```html
<!-- Primary Button - Blue -->
<button class="px-6 py-3 
               bg-primary text-on-primary 
               dark:bg-primary-dark dark:text-on-primary-dark
               rounded-full font-medium 
               shadow-sm hover:shadow-md dark:shadow-none
               hover:bg-blue-700 dark:hover:bg-blue-300
               transition-all duration-200"
        hx-post='/action' 
        hx-target='#result'>
    {{ t "action.save" .Lang }}
</button>

<!-- Secondary Button - White/Outlined -->
<button class="px-6 py-3 
               bg-surface text-primary 
               dark:bg-surface-dark dark:text-primary-dark
               rounded-full font-medium 
               border-2 border-primary dark:border-primary-dark
               hover:bg-primary-container dark:hover:bg-primary-container-dark
               transition-all duration-200">
    {{ t "action.cancel" .Lang }}
</button>

<!-- Text Button -->
<button class="px-4 py-2 
               text-primary dark:text-primary-dark
               font-medium rounded-full
               hover:bg-primary/10 dark:hover:bg-primary-dark/10
               transition-colors duration-200">
    {{ t "action.learn_more" .Lang }}
</button>

<!-- Icon Button -->
<button class="p-3 rounded-full 
               text-on-surface-variant dark:text-on-surface-variant-dark
               hover:bg-surface-variant dark:hover:bg-surface-variant-dark
               transition-colors duration-200"
        aria-label="{{ t 'action.menu' .Lang }}">
    <svg class="w-6 h-6">
        <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z"/>
    </svg>
</button>

<!-- Floating Action Button -->
<button class="fixed bottom-6 right-6 
               w-14 h-14 bg-primary dark:bg-primary-dark
               text-on-primary dark:text-on-primary-dark
               rounded-2xl shadow-lg hover:shadow-xl
               flex items-center justify-center
               transition-all duration-200"
        hx-get="/chat/new" 
        hx-target="#main">
    <svg class="w-6 h-6"><!-- Plus icon --></svg>
</button>
```

### Cards

```html
<!-- Elevated Card - White with Blue Accents -->
<div class="bg-surface dark:bg-elevation-level2-dark 
            rounded-3xl p-6 
            shadow-md dark:shadow-none 
            dark:ring-1 dark:ring-outline-dark
            hover:shadow-lg dark:hover:bg-elevation-level3-dark 
            transition-all duration-200 cursor-pointer
            border-t-4 border-primary dark:border-primary-dark"
     hx-get="/details/{{ .ID }}" 
     hx-target="#modal">
    <div class="flex items-start justify-between mb-4">
        <h3 class="text-headline-medium text-on-surface dark:text-on-surface-dark">
            {{ .Title }}
        </h3>
        <span class="px-3 py-1 text-sm rounded-full
                     bg-primary-container dark:bg-primary-container-dark
                     text-on-primary-container dark:text-on-primary-container-dark">
            {{ .Status }}
        </span>
    </div>
    <p class="text-body-large text-on-surface-variant dark:text-on-surface-variant-dark">
        {{ .Description }}
    </p>
    <div class="mt-4 flex items-center gap-4 text-sm text-outline dark:text-outline-dark">
        <span>{{ formatDateTime .UpdatedAt .Lang }}</span>
        <span>•</span>
        <span>{{ .Author }}</span>
    </div>
</div>

<!-- Outlined Card -->
<div class="border-2 border-outline dark:border-outline-dark 
            rounded-3xl p-6 
            bg-surface dark:bg-surface-dark
            hover:border-primary dark:hover:border-primary-dark
            transition-colors duration-200">
    <!-- Content -->
</div>
```

### Input Fields

```html
<!-- Material Design 3 Text Field with Floating Label -->
<div class="relative">
    <input type="text" 
           id="username"
           class="peer w-full px-4 pt-6 pb-2 
                  bg-surface-variant dark:bg-surface-variant-dark
                  text-on-surface dark:text-on-surface-dark
                  rounded-t-lg border-b-2 
                  border-outline dark:border-outline-dark
                  focus:border-primary dark:focus:border-primary-dark
                  focus:bg-surface dark:focus:bg-elevation-level1-dark
                  transition-all duration-200"
           placeholder=" "
           hx-post="/validate/username" 
           hx-trigger="keyup changed delay:500ms"
           hx-target="#username-error">
    <label for="username"
           class="absolute left-4 top-2 
                  text-xs text-on-surface-variant dark:text-on-surface-variant-dark
                  transition-all duration-200 pointer-events-none
                  peer-placeholder-shown:text-base peer-placeholder-shown:top-4
                  peer-focus:text-xs peer-focus:top-2
                  peer-focus:text-primary dark:peer-focus:text-primary-dark">
        {{ t "form.username" .Lang }}
    </label>
    <div id="username-error" class="mt-1 text-sm text-error dark:text-error-dark"></div>
</div>

<!-- Search Field -->
<div class="relative">
    <svg class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 
                text-on-surface-variant dark:text-on-surface-variant-dark">
        <!-- Search icon -->
    </svg>
    <input type="search"
           class="w-full pl-12 pr-4 py-3 
                  bg-surface-variant dark:bg-surface-variant-dark
                  text-on-surface dark:text-on-surface-dark
                  rounded-full
                  placeholder:text-on-surface-variant 
                  dark:placeholder:text-on-surface-variant-dark
                  focus:bg-surface dark:focus:bg-elevation-level1-dark
                  focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark
                  transition-all duration-200"
           placeholder="{{ t 'action.search' .Lang }}..."
           hx-post="/search" 
           hx-trigger="keyup changed delay:300ms"
           hx-target="#search-results">
</div>
```

### Navigation

```html
<!-- Navigation Rail with Language Support -->
<nav class="w-20 lg:w-64 
            bg-surface dark:bg-surface-dark
            border-r border-outline dark:border-outline-dark 
            flex flex-col transition-all duration-300">
    <!-- Logo -->
    <div class="p-4 border-b border-outline dark:border-outline-dark">
        <img src="/logo.svg" alt="GoAssistant" class="w-12 h-12 lg:w-auto">
    </div>
    
    <!-- Navigation Items -->
    <div class="flex-1 py-4">
        <a href="/" 
           class="flex items-center gap-3 px-4 py-3 mx-2 rounded-2xl
                  text-on-surface-variant dark:text-on-surface-variant-dark
                  hover:bg-primary-container dark:hover:bg-primary-container-dark
                  hover:text-on-primary-container dark:hover:text-on-primary-container-dark
                  transition-all duration-200
                  {{ if eq .CurrentPath "/" }}
                  bg-primary-container dark:bg-primary-container-dark
                  text-on-primary-container dark:text-on-primary-container-dark
                  {{ end }}"
           hx-boost="true">
            <svg class="w-6 h-6 flex-shrink-0"><!-- Home icon --></svg>
            <span class="hidden lg:block">{{ t "nav.home" .Lang }}</span>
        </a>
        <!-- More nav items -->
    </div>
    
    <!-- Bottom section with theme and language toggles -->
    <div class="p-4 border-t border-outline dark:border-outline-dark">
        <!-- Theme toggle -->
        <button class="w-full flex items-center gap-3 px-4 py-3 rounded-2xl
                       hover:bg-surface-variant dark:hover:bg-surface-variant-dark
                       transition-colors duration-200">
            <svg class="w-6 h-6"><!-- Theme icon --></svg>
            <span class="hidden lg:block">{{ t "settings.theme" .Lang }}</span>
        </button>
    </div>
</nav>

<!-- Top App Bar -->
<header class="h-16 
               bg-surface dark:bg-surface-dark
               border-b border-outline dark:border-outline-dark 
               flex items-center justify-between px-6">
    <h1 class="text-title-large text-on-surface dark:text-on-surface-dark">
        {{ .PageTitle }}
    </h1>
    
    <div class="flex items-center gap-4">
        <!-- Language Selector -->
        <div class="relative" x-data="{ open: false }">
            <button @click="open = !open"
                    class="flex items-center gap-2 px-4 py-2 rounded-full
                           hover:bg-surface-variant dark:hover:bg-surface-variant-dark
                           transition-colors duration-200">
                <svg class="w-5 h-5"><!-- Globe icon --></svg>
                <span>{{ .CurrentLangName }}</span>
            </button>
            <!-- Dropdown menu -->
        </div>
        
        <!-- User Avatar -->
        <button class="w-10 h-10 rounded-full overflow-hidden 
                       ring-2 ring-surface-variant dark:ring-surface-variant-dark">
            <img src="{{ .UserAvatar }}" alt="{{ .UserName }}">
        </button>
    </div>
</header>
```

## Interaction Patterns

### Loading States

```html
<!-- Inline Loading with Localized Text -->
<div hx-trigger="load" hx-get="/data" 
     class="htmx-request:opacity-50 transition-opacity">
    <div class="htmx-indicator flex items-center gap-3 
                text-primary dark:text-primary-dark">
        <div class="animate-spin rounded-full h-4 w-4 
                    border-2 border-current border-t-transparent"></div>
        <span>{{ t "status.loading" .Lang }}</span>
    </div>
    <!-- Content -->
</div>

<!-- Skeleton Loading for Cards -->
<div class="animate-pulse">
    <div class="h-8 bg-surface-variant dark:bg-surface-variant-dark 
                rounded-xl w-3/4 mb-4"></div>
    <div class="h-4 bg-surface-variant dark:bg-surface-variant-dark 
                rounded-lg w-full mb-2"></div>
    <div class="h-4 bg-surface-variant dark:bg-surface-variant-dark 
                rounded-lg w-5/6"></div>
</div>

<!-- Full Page Loading -->
<div class="fixed inset-0 bg-surface/80 dark:bg-surface-dark/80 
            backdrop-blur-sm z-50 flex items-center justify-center">
    <div class="bg-surface dark:bg-elevation-level3-dark 
                rounded-3xl p-8 shadow-xl dark:shadow-none
                dark:ring-1 dark:ring-outline-dark">
        <div class="flex flex-col items-center gap-4">
            <div class="w-16 h-16 border-4 border-primary-container 
                        dark:border-primary-container-dark
                        border-t-primary dark:border-t-primary-dark
                        rounded-full animate-spin"></div>
            <p class="text-title-medium text-on-surface dark:text-on-surface-dark">
                {{ t "status.processing" .Lang }}
            </p>
            <p class="text-body-medium text-on-surface-variant 
                      dark:text-on-surface-variant-dark">
                {{ t "status.please_wait" .Lang }}
            </p>
        </div>
    </div>
</div>
```

### Transitions

```html
<!-- Fade In/Out with HTMX -->
<div class="transition-opacity duration-300"
     hx-swap="outerHTML transition:true">
    <!-- Content -->
</div>

<!-- Slide In Panel -->
<div class="transform transition-transform duration-300 
            translate-x-full htmx-added:translate-x-0
            bg-surface dark:bg-surface-dark
            border-l border-outline dark:border-outline-dark">
    <!-- Panel content -->
</div>

<!-- Scale Animation for Modals -->
<div class="transform transition-all duration-300 
            scale-95 opacity-0 
            htmx-added:scale-100 htmx-added:opacity-100">
    <!-- Modal content -->
</div>
```

### Error Handling

```html
<!-- Error Message Component -->
<div class="bg-error-container dark:bg-error-container-dark 
            text-on-error-container dark:text-on-error-container-dark
            rounded-2xl p-4 flex items-start gap-3
            border border-error/20 dark:border-error-dark/20">
    <svg class="w-5 h-5 flex-shrink-0 mt-0.5">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
    </svg>
    <div class="flex-1">
        <p class="font-medium text-title-small">
            {{ t "error.title" .Lang }}
        </p>
        <p class="text-body-medium mt-1">
            {{ .ErrorMessage }}
        </p>
        <button class="mt-3 text-sm font-medium 
                       text-error dark:text-error-dark
                       hover:underline"
                hx-get="/retry"
                hx-target="#content">
            {{ t "action.retry" .Lang }}
        </button>
    </div>
    <button class="p-1 hover:bg-error/10 dark:hover:bg-error-dark/10 
                   rounded-full transition-colors"
            onclick="this.parentElement.remove()">
        <svg class="w-4 h-4"><!-- Close icon --></svg>
    </button>
</div>

<!-- Success Message -->
<div class="bg-tertiary-container dark:bg-tertiary-container-dark 
            text-on-tertiary-container dark:text-on-tertiary-container-dark
            rounded-2xl p-4 flex items-center gap-3">
    <svg class="w-5 h-5 flex-shrink-0">
        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
    </svg>
    <p class="flex-1 text-body-large">
        {{ t "success.saved" .Lang }}
    </p>
</div>

<!-- Form Validation Error -->
<div class="mt-2 text-sm text-error dark:text-error-dark 
            flex items-center gap-1">
    <svg class="w-4 h-4">
        <circle cx="12" cy="12" r="10"/>
        <line x1="12" y1="8" x2="12" y2="12"/>
        <line x1="12" y1="16" x2="12.01" y2="16"/>
    </svg>
    <span>{{ t .ValidationError .Lang }}</span>
</div>
```

### Multilingual Error Messages
```go
// Common error messages translations
var errorTranslations = map[string]map[string]string{
    "en": {
        "error.title": "Something went wrong",
        "error.network": "Network error. Please check your connection.",
        "error.server": "Server error. Please try again later.",
        "error.validation": "Please check your input and try again.",
        "error.not_found": "The requested resource was not found.",
        "error.unauthorized": "You don't have permission to access this resource.",
        "error.rate_limit": "Too many requests. Please wait a moment.",
        "validation.required": "This field is required",
        "validation.email": "Please enter a valid email address",
        "validation.min_length": "Must be at least %d characters",
        "validation.max_length": "Must be less than %d characters",
    },
    "zh-TW": {
        "error.title": "發生錯誤",
        "error.network": "網路錯誤，請檢查您的連線。",
        "error.server": "伺服器錯誤，請稍後再試。",
        "error.validation": "請檢查您的輸入並重試。",
        "error.not_found": "找不到請求的資源。",
        "error.unauthorized": "您沒有權限存取此資源。",
        "error.rate_limit": "請求過於頻繁，請稍候再試。",
        "validation.required": "此欄位為必填",
        "validation.email": "請輸入有效的電子郵件地址",
        "validation.min_length": "至少需要 %d 個字元",
        "validation.max_length": "不能超過 %d 個字元",
    },
}
```

## Responsive Design

### Breakpoints
- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px

### Layout Adaptations

```html
<!-- Responsive Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Cards -->
</div>

<!-- Responsive Navigation -->
<nav class="fixed bottom-0 left-0 right-0 md:static md:w-20 
            lg:w-64 bg-surface border-t md:border-t-0 
            md:border-r border-outline">
    <!-- Navigation items -->
</nav>
```

## Accessibility

### ARIA Labels
- All interactive elements must have descriptive labels
- Use `role` attributes for custom components
- Implement keyboard navigation

### Focus Management
```css
/* Focus Visible */
.focus-visible:focus {
    outline: 2px solid var(--md-sys-color-primary);
    outline-offset: 2px;
}
```

### Color Contrast
- Ensure WCAG AA compliance (4.5:1 for normal text, 3:1 for large text)
- Blue (#0061A4) on white provides 7.2:1 contrast ratio ✓
- Test with color blindness simulators
- Provide high contrast mode option
- Use Material Design 3 color contrast tools for validation

## Performance Guidelines

### HTMX Best Practices
1. Use `hx-trigger="intersect"` for lazy loading
2. Implement `hx-swap="innerHTML"` for partial updates
3. Add loading indicators with `htmx-indicator`
4. Use `hx-boost` for navigation

### Asset Optimization
1. Inline critical CSS
2. Lazy load images with `loading="lazy"`
3. Use WebP format for images
4. Minimize HTTP requests

### Templ Components
1. Create reusable components for common patterns
2. Use composition over inheritance
3. Keep templates simple and focused
4. Leverage Go's type safety

## Dark Mode

### Implementation Strategy

#### 1. Theme Detection and Switching
```html
<!-- Theme Toggle Component -->
<div class="flex items-center gap-2">
    <button id="theme-toggle" 
            class="relative w-14 h-8 bg-surface-variant rounded-full 
                   transition-colors duration-300"
            hx-post="/api/theme/toggle" 
            hx-swap="none">
        <span class="absolute top-1 left-1 w-6 h-6 bg-primary rounded-full 
                     transition-transform duration-300
                     dark:translate-x-6"></span>
    </button>
    <span class="text-label-large">{{ t "settings.dark_mode" .Lang }}</span>
</div>

<!-- Automatic theme detection script -->
<script>
    // Check user preference
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
        document.documentElement.setAttribute('data-theme', 'dark');
    }
    
    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
            document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
        }
    });
</script>
```

#### 2. Component Adaptations
```html
<!-- Card Component with Dark Mode Support -->
<div class="bg-surface dark:bg-surface-dark 
            text-on-surface dark:text-on-surface-dark
            rounded-3xl p-6 shadow-md dark:shadow-none 
            dark:ring-1 dark:ring-outline-dark
            hover:shadow-lg dark:hover:bg-elevation-level1-dark 
            transition-all duration-200">
    <h3 class="text-headline-medium mb-2">{{ .Title }}</h3>
    <p class="text-body-large text-on-surface-variant 
              dark:text-on-surface-variant-dark">
        {{ .Description }}
    </p>
</div>

<!-- Primary Button with Dark Mode -->
<button class="px-6 py-3 
               bg-primary dark:bg-primary-dark
               text-on-primary dark:text-on-primary-dark
               rounded-full font-medium 
               shadow-sm hover:shadow-md dark:shadow-none
               transition-all duration-200
               hover:bg-primary-container dark:hover:bg-primary-container-dark"
        hx-post="/action" 
        hx-target="#result">
    {{ t "action.save" .Lang }}
</button>
```

#### 3. Elevation System for Dark Mode
Material Design 3 uses color overlays instead of shadows in dark mode:

```css
/* Dark Mode Elevation Levels */
.dark .elevation-1 { background-color: var(--md-sys-elevation-level1-dark); }
.dark .elevation-2 { background-color: var(--md-sys-elevation-level2-dark); }
.dark .elevation-3 { background-color: var(--md-sys-elevation-level3-dark); }
.dark .elevation-4 { background-color: var(--md-sys-elevation-level4-dark); }
.dark .elevation-5 { background-color: var(--md-sys-elevation-level5-dark); }

/* Remove shadows in dark mode */
.dark .shadow-sm,
.dark .shadow-md,
.dark .shadow-lg { box-shadow: none; }
```

### Color Adjustments
- Primary surfaces use elevation tints in dark mode
- Increased contrast for text readability
- Subtle borders replace shadows
- Status colors remain vibrant but adjusted for dark backgrounds

### Testing Requirements
1. Test all UI components in both light and dark modes
2. Verify color contrast meets WCAG AA standards
3. Ensure smooth transitions between themes
4. Check that user preference persists across sessions

## Animation Guidelines

### Material Design 3 Motion Principles
- **Expressive**: Animations should feel natural and fluid
- **Focused**: Draw attention to important elements
- **Seamless**: Smooth transitions between states

### Micro-interactions
- Button hover states: 150ms ease-out
- Card elevation: 200ms ease-in-out
- Loading spinners: 1s linear infinite
- Fade in/out: 300ms ease-in-out
- Color transitions: 200ms ease

### Page Transitions
- Use HTMX swapping with transitions
- Implement smooth scrolling for anchor links
- Add subtle slide effects for panels (300ms)
- Stagger animations for list items (50ms delay)

### Language Switch Animation
- Fade out current content (150ms)
- Swap content
- Fade in new content (150ms)
- Preserve scroll position

## Testing Guidelines

### Visual Testing
1. Test all breakpoints (mobile, tablet, desktop)
2. Verify dark mode appearance
3. Check loading states
4. Validate error scenarios
5. Test with both English and Traditional Chinese
6. Verify text doesn't overflow in either language

### Interaction Testing
1. Keyboard navigation
2. Screen reader compatibility (test with both languages)
3. Touch targets (minimum 44x44px)
4. Form validation feedback in correct language
5. Theme switching persistence
6. Language switching without data loss

### Localization Testing
1. Date/time format correctness
2. Number formatting by locale
3. Translation completeness
4. Character encoding (UTF-8)
5. Font rendering for Chinese characters
6. Layout adaptation for longer text

## Development Tips

### Using TemplUI Component Library

**Important**: Before building custom components, always check [TemplUI](https://templui.io/) for ready-to-use Material Design 3 components. This library provides:

#### Available Components from TemplUI:
1. **Navigation Components**
  - Navigation Rails
  - Navigation Drawers
  - Top App Bars
  - Bottom Navigation
  - Tabs

2. **Input Components**
  - Text Fields (with floating labels)
  - Select Menus
  - Checkboxes & Radio Buttons
  - Switches
  - Sliders
  - Date/Time Pickers

3. **Buttons & Actions**
  - Filled Buttons
  - Outlined Buttons
  - Text Buttons
  - Icon Buttons
  - Floating Action Buttons
  - Extended FABs

4. **Cards & Containers**
  - Elevated Cards
  - Filled Cards
  - Outlined Cards
  - List Items
  - Dialogs
  - Bottom Sheets

5. **Feedback Components**
  - Progress Indicators
  - Snackbars
  - Badges
  - Tooltips
  - Banners

6. **Layout Components**
  - Responsive Grids
  - Dividers
  - Spacers

#### Integration Strategy:

```go
// 1. Search TemplUI for the component you need
// Example: https://templui.io/components/buttons

// 2. Copy the Templ component code
templ PrimaryButton(text string, attrs templ.Attributes) {
    <button 
        class="inline-flex items-center justify-center px-6 py-3
               bg-primary text-on-primary rounded-full
               font-medium shadow-sm hover:shadow-md
               transition-all duration-200"
        { attrs... }>
        { text }
    </button>
}

// 3. Customize for your needs (add translations, dark mode, etc.)
templ LocalizedButton(key string, lang string, attrs templ.Attributes) {
    <button 
        class="inline-flex items-center justify-center px-6 py-3
               bg-primary dark:bg-primary-dark
               text-on-primary dark:text-on-primary-dark
               rounded-full font-medium shadow-sm hover:shadow-md
               dark:shadow-none transition-all duration-200"
        { attrs... }>
        { t(key, lang) }
    </button>
}
```

#### Customization Guidelines:

When using TemplUI components:

1. **Add Dark Mode Support**
  - Add `dark:` variants for colors
  - Adjust shadows and elevations
  - Test in both themes

2. **Add Internationalization**
  - Replace hardcoded text with translation functions
  - Consider text expansion for different languages
  - Test with longest translations

3. **Add HTMX Attributes**
  - Add `hx-*` attributes as needed
  - Include loading states
  - Add proper error handling

4. **Maintain Consistency**
  - Use the blue/white color scheme
  - Keep Material Design 3 principles
  - Ensure responsive behavior

#### Example Workflow:

```go
// 1. Check if TemplUI has the component
// Search: https://templui.io/components/cards

// 2. If found, copy and adapt:
templ ArticleCard(article Article, lang string) {
    // Based on TemplUI card component
    <article 
        class="bg-surface dark:bg-elevation-level2-dark 
               rounded-3xl p-6 shadow-md dark:shadow-none
               hover:shadow-lg transition-all duration-200"
        hx-get={ fmt.Sprintf("/articles/%s", article.ID) }
        hx-target="#content"
        hx-push-url="true">
        
        <h3 class="text-headline-medium mb-2">
            { article.Title }
        </h3>
        <p class="text-body-large text-on-surface-variant">
            { article.Summary }
        </p>
        <time class="text-label-medium text-outline mt-4 block">
            { formatDateTime(article.CreatedAt, lang) }
        </time>
    </article>
}

// 3. If not found in TemplUI, build custom following MD3 guidelines
```

#### Priority Components to Use from TemplUI:

1. **Form Components** - Complex validation and states
2. **Navigation** - Responsive behavior built-in
3. **Dialogs/Modals** - Accessibility handled
4. **Data Tables** - Sorting, pagination included
5. **File Upload** - Drag & drop functionality

#### When to Build Custom:

Only build custom components when:
- TemplUI doesn't have what you need
- Highly specific to GoAssistant features
- Need deep integration with backend logic
- Complex real-time features with WebSockets

### Time-Saving Tips:

1. **Start with TemplUI Search**: Always search first before building
2. **Use Component Composition**: Combine TemplUI components
3. **Create Wrapper Components**: Wrap TemplUI components with your logic
4. **Maintain a Component Registry**: Document which TemplUI components you're using
5. **Version Control**: Note TemplUI version for consistency

This approach can save 60-80% of UI development time while maintaining high quality and consistency.

## Implementation Notes

### Templ Structure
```
templates/
├── layouts/
│   ├── base.templ
│   ├── app.templ
│   └── auth.templ
├── components/
│   ├── button.templ
│   ├── card.templ
│   ├── input.templ
│   ├── modal.templ
│   └── navigation.templ
├── pages/
│   ├── dashboard.templ
│   ├── chat.templ
│   ├── tools.templ
│   ├── development.templ
│   ├── database.templ
│   ├── infrastructure.templ
│   └── settings.templ
├── partials/
│   ├── nav.templ
│   ├── header.templ
│   ├── footer.templ
│   └── language-selector.templ
└── i18n/
    ├── en.json
    └── zh-TW.json
```

### HTMX Headers
- Use `HX-Trigger` for event coordination
- Implement `HX-Push-URL` for history management
- Add `HX-Retarget` for dynamic targeting
- Include language preference in custom headers

### Theme and Language Persistence
```go
// Middleware to handle theme and language preferences
func PreferencesMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Check cookie for saved preferences
        lang := "en" // default
        theme := "light" // default
        
        if cookie, err := r.Cookie("lang"); err == nil {
            lang = cookie.Value
        }
        if cookie, err := r.Cookie("theme"); err == nil {
            theme = cookie.Value
        }
        
        // Add to context
        ctx := context.WithValue(r.Context(), "lang", lang)
        ctx = context.WithValue(ctx, "theme", theme)
        
        next.ServeHTTP(w, r.WithContext(ctx))
    })
}
```

### Blue & White Theme Summary
The design system uses a clean blue and white color palette that:
- **Primary Blue** (#0061A4): Used for primary actions, links, and focus states
- **Pure White** (#FDFCFF): Clean backgrounds and surfaces
- **Blue-Grey** accents for secondary elements
- **High contrast** in both light and dark modes
- **Consistent tinting** system for elevation in dark mode

### Internationalization Best Practices
1. **Always use translation keys** instead of hardcoded text
2. **Provide context** in translation keys (e.g., `nav.home` vs just `home`)
3. **Format dates/times** according to locale preferences
4. **Test UI with longest translations** to ensure layout doesn't break
5. **Use language-specific fonts** for optimal readability
6. **Consider cultural differences** in icons and imagery

### Accessibility Considerations
1. **Language attributes**: Always set `lang` attribute on HTML elements
2. **ARIA labels**: Translate all ARIA labels and descriptions
3. **Form validation**: Provide clear, localized error messages
4. **Screen reader support**: Test with screen readers in both languages
5. **Keyboard navigation**: Ensure all interactive elements are accessible

### Development Resources Quick Reference

1. **TemplUI Component Library**: https://templui.io/
  - Search components before building
  - Free Material Design 3 components
  - Templ-native implementation
  - Copy-paste ready

2. **Material Design 3 Guidelines**: https://m3.material.io/
  - Color system generator
  - Component specifications
  - Motion guidelines
  - Accessibility standards

3. **Tailwind CSS Docs**: https://tailwindcss.com/
  - Utility classes reference
  - Dark mode utilities
  - Responsive design helpers

4. **HTMX Documentation**: https://htmx.org/
  - Attributes reference
  - Events and triggers
  - Extensions

### Component Development Workflow

```mermaid
graph TD
    A[Need a Component] --> B{Check TemplUI}
    B -->|Found| C[Copy Component Code]
    B -->|Not Found| D[Build Custom]
    C --> E[Add i18n Support]
    E --> F[Add Dark Mode]
    F --> G[Add HTMX Attributes]
    G --> H[Test & Deploy]
    D --> I[Follow MD3 Guidelines]
    I --> H
```

#### Real-World TemplUI Integration Examples:

```go
// Example 1: Using TemplUI Navigation Rail for GoAssistant
// Original from: https://templui.io/components/navigation-rail

// Step 1: Copy TemplUI component
// Step 2: Customize for GoAssistant needs
templ NavigationRail(currentPath string, lang string) {
    <nav class="w-20 lg:w-64 h-screen 
                bg-surface dark:bg-surface-dark
                border-r border-outline dark:border-outline-dark
                flex flex-col transition-all duration-300">
        
        // Logo section
        <div class="p-4 border-b border-outline dark:border-outline-dark">
            <img src="/logo.svg" alt="GoAssistant" 
                 class="w-12 h-12 lg:w-auto transition-all">
        </div>
        
        // Navigation items with i18n
        <div class="flex-1 py-4">
            @NavItem("/", "nav.home", "home", currentPath, lang)
            @NavItem("/chat", "nav.chat", "message", currentPath, lang)
            @NavItem("/tools", "nav.tools", "build", currentPath, lang)
            @NavItem("/development", "nav.development", "code", currentPath, lang)
            @NavItem("/database", "nav.database", "database", currentPath, lang)
            @NavItem("/infrastructure", "nav.infrastructure", "cloud", currentPath, lang)
        </div>
        
        // Bottom section
        <div class="p-4 border-t border-outline dark:border-outline-dark">
            @ThemeToggle(lang)
            @LanguageSelector(lang)
        </div>
    </nav>
}

// Example 2: Using TemplUI Text Field with Validation
// Original from: https://templui.io/components/text-fields

templ ValidatedTextField(name, label, value, errorKey string, lang string) {
    <div class="relative">
        // TemplUI structure with GoAssistant customizations
        <input 
            type="text"
            id={ name }
            name={ name }
            value={ value }
            class="peer w-full px-4 pt-6 pb-2 
                   bg-surface-variant dark:bg-surface-variant-dark
                   text-on-surface dark:text-on-surface-dark
                   rounded-t-lg border-b-2
                   border-outline dark:border-outline-dark
                   focus:border-primary dark:focus:border-primary-dark
                   focus:bg-surface dark:focus:bg-elevation-level1-dark
                   transition-all duration-200"
            placeholder=" "
            hx-post={ fmt.Sprintf("/validate/%s", name) }
            hx-trigger="keyup changed delay:500ms"
            hx-target={ fmt.Sprintf("#%s-error", name) }
            hx-swap="innerHTML"
        />
        <label 
            for={ name }
            class="absolute left-4 top-2 text-xs
                   text-on-surface-variant dark:text-on-surface-variant-dark
                   transition-all duration-200 pointer-events-none
                   peer-placeholder-shown:text-base peer-placeholder-shown:top-4
                   peer-focus:text-xs peer-focus:top-2
                   peer-focus:text-primary dark:peer-focus:text-primary-dark">
            { t(label, lang) }
        </label>
        <div id={ fmt.Sprintf("%s-error", name) } 
             class="mt-1 text-sm text-error dark:text-error-dark">
            if errorKey != "" {
                { t(errorKey, lang) }
            }
        </div>
    </div>
}

// Example 3: Using TemplUI Card with HTMX
// Original from: https://templui.io/components/cards

templ AgentCard(agent Agent, lang string) {
    <article 
        class="relative overflow-hidden
               bg-surface dark:bg-elevation-level2-dark 
               rounded-3xl shadow-md dark:shadow-none
               hover:shadow-lg dark:hover:bg-elevation-level3-dark
               transition-all duration-200 cursor-pointer
               group"
        hx-get={ fmt.Sprintf("/agents/%s", agent.ID) }
        hx-target="#main-content"
        hx-push-url="true">
        
        // Gradient background for visual interest
        <div class="absolute inset-0 bg-gradient-to-br 
                    from-primary/10 to-transparent 
                    dark:from-primary-dark/20"></div>
        
        // Content
        <div class="relative p-6">
            <div class="flex items-start justify-between mb-4">
                <div class="p-3 bg-primary-container dark:bg-primary-container-dark 
                            rounded-2xl">
                    @icon(agent.Icon, "w-6 h-6 text-on-primary-container 
                                      dark:text-on-primary-container-dark")
                </div>
                @StatusBadge(agent.Status, lang)
            </div>
            
            <h3 class="text-headline-medium mb-2 
                       group-hover:text-primary dark:group-hover:text-primary-dark
                       transition-colors">
                { t(agent.NameKey, lang) }
            </h3>
            
            <p class="text-body-large text-on-surface-variant 
                      dark:text-on-surface-variant-dark">
                { t(agent.DescriptionKey, lang) }
            </p>
            
            <div class="mt-4 flex items-center gap-4">
                <span class="text-label-medium text-outline dark:text-outline-dark">
                    { formatNumber(agent.TasksCompleted, lang) } { t("agent.tasks", lang) }
                </span>
                <span class="text-label-medium text-outline dark:text-outline-dark">
                    { agent.Uptime }% { t("agent.uptime", lang) }
                </span>
            </div>
        </div>
        
        // Loading indicator for HTMX
        <div class="htmx-indicator absolute inset-0 
                    bg-surface/80 dark:bg-surface-dark/80
                    flex items-center justify-center">
            <div class="animate-spin rounded-full h-8 w-8 
                        border-2 border-primary dark:border-primary-dark
                        border-t-transparent"></div>
        </div>
    </article>
}
```

#### TemplUI Integration Checklist:

- [ ] Search TemplUI for existing component
- [ ] Copy base component structure
- [ ] Add dark mode classes (`dark:` variants)
- [ ] Replace text with translation functions
- [ ] Add HTMX attributes for interactivity
- [ ] Include loading indicators
- [ ] Test responsive behavior
- [ ] Verify accessibility
- [ ] Document any major modifications

This approach ensures rapid development while maintaining consistency with Material Design 3 principles and GoAssistant's specific requirements.