package templates

// CardVariant defines the card style variants
type CardVariant string

const (
	CardElevated CardVariant = "elevated"
	CardFilled   CardVariant = "filled"
	CardOutlined CardVariant = "outlined"
)

// Card implements Material Design 3 card component
templ Card(variant CardVariant, clickable bool, attrs templ.Attributes) {
	<div 
		{ attrs... }
		class={
			"rounded-medium transition-standard",
			getCardVariantClasses(variant),
			templ.KV("cursor-pointer hover:shadow-elevation-2", clickable && variant == CardElevated),
			templ.KV("cursor-pointer hover:shadow-elevation-1", clickable && variant != CardElevated)
		}
	>
		{ children... }
	</div>
}

// CardHeader implements card header section
templ CardHeader(title string, subtitle string, icon string, actions templ.Component) {
	<div class="p-4 pb-0">
		<div class="flex items-start justify-between">
			<div class="flex items-start space-x-4 flex-1 min-w-0">
				if icon != "" {
					<div class="flex-shrink-0">
						<span class="material-symbols-outlined text-on-surface-variant text-2xl">{ icon }</span>
					</div>
				}
				<div class="flex-1 min-w-0">
					<h3 class="text-title-large text-on-surface font-medium truncate">{ title }</h3>
					if subtitle != "" {
						<p class="text-body-medium text-on-surface-variant mt-1">{ subtitle }</p>
					}
				</div>
			</div>
			if actions != nil {
				<div class="flex-shrink-0 ml-4">
					@actions
				</div>
			}
		</div>
	</div>
}

// CardContent implements card content section
templ CardContent() {
	<div class="p-4">
		{ children... }
	</div>
}

// CardActions implements card actions section
templ CardActions(alignment string) {
	<div class={
		"p-4 pt-0 flex space-x-2",
		templ.KV("justify-start", alignment == "start"),
		templ.KV("justify-end", alignment == "end"),
		templ.KV("justify-center", alignment == "center"),
		templ.KV("justify-end", alignment == "")
	}>
		{ children... }
	</div>
}

// StatCard implements a statistics display card
templ StatCard(title string, value string, subtitle string, icon string, trend string) {
	@Card(CardElevated, false, templ.Attributes{}) {
		<div class="p-6">
			<div class="flex items-start justify-between">
				<div class="flex-1">
					<p class="text-body-medium text-on-surface-variant mb-1">{ title }</p>
					<p class="text-headline-medium text-on-surface font-medium">{ value }</p>
					if subtitle != "" {
						<p class="text-body-small text-on-surface-variant mt-1">{ subtitle }</p>
					}
				</div>
				if icon != "" {
					<div class="flex-shrink-0 ml-4">
						<div class="w-12 h-12 bg-primary-container rounded-large flex items-center justify-center">
							<span class="material-symbols-outlined text-on-primary-container">{ icon }</span>
						</div>
					</div>
				}
			</div>
			if trend != "" {
				<div class="mt-4 pt-4 border-t border-outline-variant">
					<p class="text-body-small text-on-surface-variant">{ trend }</p>
				</div>
			}
		</div>
	}
}

// AgentCard implements an agent status card
templ AgentCard(name string, description string, status string, isActive bool, href string) {
	@Card(CardElevated, true, templ.Attributes{
		"hx-get": href,
		"hx-boost": "true",
	}) {
		<div class="p-6">
			<div class="flex items-start justify-between mb-4">
				<div class="flex-1">
					<h3 class="text-title-large text-on-surface font-medium mb-2">{ name }</h3>
					<p class="text-body-medium text-on-surface-variant">{ description }</p>
				</div>
				<div class="flex-shrink-0 ml-4">
					<div class={
						"w-3 h-3 rounded-full",
						templ.KV("bg-primary", isActive),
						templ.KV("bg-outline", !isActive)
					}></div>
				</div>
			</div>
			<div class="flex items-center justify-between">
				<span class={
					"text-label-medium px-3 py-1 rounded-large",
					templ.KV("bg-primary-container text-on-primary-container", isActive),
					templ.KV("bg-surface-variant text-on-surface-variant", !isActive)
				}>
					{ status }
				</span>
				<span class="material-symbols-outlined text-on-surface-variant">arrow_forward</span>
			</div>
		</div>
	}
}

// ToolCard implements a tool status card
templ ToolCard(name string, description string, status string, isActive bool, lastUsed string) {
	@Card(CardOutlined, true, templ.Attributes{}) {
		<div class="p-4">
			<div class="flex items-start justify-between mb-3">
				<div class="flex-1">
					<h4 class="text-title-medium text-on-surface font-medium">{ name }</h4>
					<p class="text-body-small text-on-surface-variant mt-1">{ description }</p>
				</div>
				<div class={
					"w-2 h-2 rounded-full flex-shrink-0 mt-2",
					templ.KV("bg-primary", isActive),
					templ.KV("bg-outline", !isActive)
				}></div>
			</div>
			<div class="flex items-center justify-between text-body-small">
				<span class={
					"px-2 py-1 rounded-small",
					templ.KV("bg-primary-container text-on-primary-container", isActive),
					templ.KV("bg-surface-variant text-on-surface-variant", !isActive)
				}>
					{ status }
				</span>
				if lastUsed != "" {
					<span class="text-on-surface-variant">{ lastUsed }</span>
				}
			</div>
		</div>
	}
}

// Helper function for card variant classes
func getCardVariantClasses(variant CardVariant) string {
	switch variant {
	case CardElevated:
		return "bg-surface-container-low shadow-elevation-1"
	case CardFilled:
		return "bg-surface-container-highest"
	case CardOutlined:
		return "bg-surface border border-outline-variant"
	default:
		return "bg-surface-container-low shadow-elevation-1"
	}
}
