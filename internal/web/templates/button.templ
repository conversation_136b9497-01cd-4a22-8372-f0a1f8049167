package templates

// ButtonVariant defines the button style variants
type ButtonVariant string

const (
	ButtonFilled   ButtonVariant = "filled"
	ButtonOutlined ButtonVariant = "outlined"
	ButtonText     ButtonVariant = "text"
	ButtonElevated ButtonVariant = "elevated"
	ButtonTonal    ButtonVariant = "tonal"
)

// ButtonSize defines the button size variants
type ButtonSize string

const (
	ButtonSizeSmall  ButtonSize = "small"
	ButtonSizeMedium ButtonSize = "medium"
	ButtonSizeLarge  ButtonSize = "large"
)

// Button implements Material Design 3 button component
templ Button(variant ButtonVariant, size ButtonSize, disabled bool, icon string, text string, attrs templ.Attributes) {
	<button 
		{ attrs... }
		disabled?={ disabled }
		class={
			"inline-flex items-center justify-center font-medium transition-standard focus-visible",
			getButtonVariantClasses(variant),
			getButtonSizeClasses(size),
			templ.KV("opacity-38 cursor-not-allowed", disabled)
		}
	>
		if icon != "" {
			<span class="material-symbols-outlined mr-2">{ icon }</span>
		}
		{ text }
	</button>
}

// IconButton implements Material Design 3 icon button
templ IconButton(variant ButtonVariant, disabled bool, icon string, ariaLabel string, attrs templ.Attributes) {
	<button 
		{ attrs... }
		disabled?={ disabled }
		aria-label={ ariaLabel }
		class={
			"inline-flex items-center justify-center w-10 h-10 rounded-large transition-standard focus-visible",
			getIconButtonVariantClasses(variant),
			templ.KV("opacity-38 cursor-not-allowed", disabled)
		}
	>
		<span class="material-symbols-outlined">{ icon }</span>
	</button>
}

// FAB implements Material Design 3 floating action button
templ FAB(variant string, icon string, text string, attrs templ.Attributes) {
	<button 
		{ attrs... }
		class={
			"inline-flex items-center justify-center font-medium transition-standard focus-visible shadow-elevation-3 hover:shadow-elevation-4",
			getFABVariantClasses(variant),
			templ.KV("rounded-large px-4 h-14", text != ""),
			templ.KV("rounded-large w-14 h-14", text == "")
		}
	>
		<span class="material-symbols-outlined">{ icon }</span>
		if text != "" {
			<span class="ml-2 text-label-large">{ text }</span>
		}
	</button>
}

// Helper functions for button styling
func getButtonVariantClasses(variant ButtonVariant) string {
	switch variant {
	case ButtonFilled:
		return "bg-primary text-on-primary hover:shadow-elevation-1 rounded-large px-6 h-10"
	case ButtonOutlined:
		return "border border-outline text-primary hover:bg-primary hover:bg-opacity-8 rounded-large px-6 h-10"
	case ButtonText:
		return "text-primary hover:bg-primary hover:bg-opacity-8 rounded-large px-3 h-10"
	case ButtonElevated:
		return "bg-surface-container-low text-primary shadow-elevation-1 hover:shadow-elevation-2 rounded-large px-6 h-10"
	case ButtonTonal:
		return "bg-secondary-container text-on-secondary-container hover:shadow-elevation-1 rounded-large px-6 h-10"
	default:
		return "bg-primary text-on-primary hover:shadow-elevation-1 rounded-large px-6 h-10"
	}
}

func getButtonSizeClasses(size ButtonSize) string {
	switch size {
	case ButtonSizeSmall:
		return "text-label-medium h-8 px-4"
	case ButtonSizeLarge:
		return "text-label-large h-12 px-8"
	default:
		return "text-label-large h-10 px-6"
	}
}

func getIconButtonVariantClasses(variant ButtonVariant) string {
	switch variant {
	case ButtonFilled:
		return "bg-primary text-on-primary hover:shadow-elevation-1"
	case ButtonOutlined:
		return "border border-outline text-on-surface-variant hover:bg-on-surface hover:bg-opacity-8"
	case ButtonTonal:
		return "bg-secondary-container text-on-secondary-container hover:shadow-elevation-1"
	default:
		return "text-on-surface-variant hover:bg-on-surface hover:bg-opacity-8"
	}
}

func getFABVariantClasses(variant string) string {
	switch variant {
	case "primary":
		return "bg-primary-container text-on-primary-container"
	case "secondary":
		return "bg-secondary-container text-on-secondary-container"
	case "tertiary":
		return "bg-tertiary-container text-on-tertiary-container"
	case "surface":
		return "bg-surface-container-high text-primary"
	default:
		return "bg-primary-container text-on-primary-container"
	}
}
