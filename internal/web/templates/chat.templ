package templates

import (
	"fmt"
	"github.com/koopa0/assistant-go/internal/web/i18n"
)

// ModernChatPage implements a modern, clean chat interface inspired by leading AI platforms
templ ModernChatPage(data ChatPageData) {
	<div class="h-full flex bg-background">
		<!-- Modern Sidebar -->
		<div class="w-80 bg-surface border-r border-outline-variant flex flex-col">
			<!-- Sidebar Header -->
			<div class="p-6 border-b border-outline-variant">
				<div class="flex items-center gap-3 mb-4">
					<div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center shadow-sm">
						<span class="material-symbols-outlined text-white text-xl">auto_awesome</span>
					</div>
					<div>
						<h1 class="text-lg font-semibold text-on-surface">AI Chat</h1>
						<p class="text-sm text-on-surface-variant">Smart conversations</p>
					</div>
				</div>
				<button 
					class="w-full flex items-center justify-center gap-2 px-4 py-3 bg-primary text-on-primary rounded-xl hover:bg-primary/90 transition-all duration-200 font-medium"
					hx-post="/api/chat/new" 
					hx-target="#main-chat"
					hx-swap="innerHTML"
				>
					<span class="material-symbols-outlined">add</span>
					New Chat
				</button>
			</div>

			<!-- Chat History -->
			<div class="flex-1 overflow-y-auto p-4 space-y-2">
				<div class="text-xs font-medium text-on-surface-variant uppercase tracking-wide px-2 mb-3">
					Recent Chats
				</div>
				@ChatHistoryItem("今天", "Go專案架構分析", "剛才", true)
				@ChatHistoryItem("昨天", "資料庫優化建議", "昨天 14:30", false)
				@ChatHistoryItem("昨天", "Kubernetes部署問題", "昨天 10:15", false)
				@ChatHistoryItem("2天前", "API設計討論", "週二", false)
			</div>

			<!-- Agent Selector -->
			<div class="p-4 border-t border-outline-variant">
				<div class="text-xs font-medium text-on-surface-variant uppercase tracking-wide mb-3">
					AI Assistants
				</div>
				<div class="space-y-2">
					for _, agent := range data.AvailableAgents {
						@CompactAgentCard(agent, data.ActiveAgent.ID == agent.ID)
					}
				</div>
			</div>
		</div>

		<!-- Main Chat Area -->
		<div class="flex-1 flex flex-col" id="main-chat">
			<!-- Minimal Header -->
			<div class="flex items-center justify-between px-6 py-4 border-b border-outline-variant bg-surface/50 backdrop-blur-sm">
				<div class="flex items-center gap-3">
					<div class="w-8 h-8 bg-primary-container rounded-lg flex items-center justify-center">
						<span class="material-symbols-outlined text-primary text-lg">psychology</span>
					</div>
					<div>
						<h2 class="font-medium text-on-surface">{ data.ActiveAgent.Name }</h2>
						<p class="text-sm text-on-surface-variant">{ data.ActiveAgent.Description }</p>
					</div>
				</div>
				<div class="flex items-center gap-1">
					<button 
						class="p-2 hover:bg-surface-variant rounded-lg transition-colors"
						hx-get="/api/chat/export"
						hx-trigger="click"
						title="Export conversation"
					>
						<span class="material-symbols-outlined text-on-surface-variant">download</span>
					</button>
					<button 
						class="p-2 hover:bg-surface-variant rounded-lg transition-colors"
						onclick="if(confirm('Clear this conversation?')) { clearChat(); }"
						title="Clear chat"
					>
						<span class="material-symbols-outlined text-on-surface-variant">delete</span>
					</button>
				</div>
			</div>

			<!-- Messages Area with Modern Design -->
			<div 
				id="messages-container" 
				class="flex-1 overflow-y-auto px-6 py-6"
			>
				if len(data.Messages) == 0 {
					<!-- Modern Empty State -->
					<div class="h-full flex items-center justify-center">
						<div class="text-center max-w-2xl">
							<!-- Gradient Icon -->
							<div class="w-16 h-16 bg-gradient-to-br from-primary via-secondary to-tertiary rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
								<span class="material-symbols-outlined text-white text-2xl">auto_awesome</span>
							</div>
							<h2 class="text-2xl font-semibold text-on-surface mb-3">
								開始與AI助手對話
							</h2>
							<p class="text-on-surface-variant mb-8 text-lg">
								選擇一個話題開始，或者直接提出您的問題
							</p>
							
							<!-- Modern Suggestion Cards -->
							<div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
								@ModernSuggestionCard("分析Go專案架構", "code", "幫我分析這個Go專案的架構和設計模式")
								@ModernSuggestionCard("資料庫優化", "storage", "檢查並優化我的資料庫設計和查詢")
								@ModernSuggestionCard("Kubernetes部署", "cloud", "協助優化我的Kubernetes配置")
								@ModernSuggestionCard("性能調優", "speed", "分析系統性能瓶頸和優化建議")
							</div>
						</div>
					</div>
				} else {
					<!-- Message Stream -->
					<div class="max-w-4xl mx-auto space-y-6">
						for _, message := range data.Messages {
							@ModernChatMessage(message, data.Lang)
						}
					</div>
				}
			</div>

			<!-- Modern Input Area -->
			<div class="p-6 border-t border-outline-variant bg-surface/50 backdrop-blur-sm">
				<div class="max-w-4xl mx-auto">
					<form 
						id="chat-form" 
						hx-post="/api/chat/send"
						hx-target="#messages-container .space-y-6"
						hx-swap="beforeend"
						hx-on:htmx:after-request="clearInput(); scrollToBottom();"
						class="relative"
					>
						<!-- Modern Input Container -->
						<div class="relative bg-surface border border-outline-variant rounded-2xl shadow-sm hover:shadow-md transition-all duration-200 focus-within:border-primary focus-within:shadow-lg">
							<!-- Input Area -->
							<div class="flex items-end gap-3 p-4">
								<!-- Attachment Button -->
								<button 
									type="button" 
									class="p-2 hover:bg-surface-variant rounded-lg transition-colors flex-shrink-0"
									onclick="document.getElementById('file-upload').click()"
									title="Attach files"
								>
									<span class="material-symbols-outlined text-on-surface-variant">attach_file</span>
								</button>
								<input type="file" id="file-upload" class="hidden" multiple accept=".txt,.md,.go,.js,.py,.json,.yaml,.pdf">

								<!-- Text Input -->
								<div class="flex-1 relative">
									<textarea
										id="message-input"
										name="message"
										placeholder="輸入您的問題或想法..."
										class="w-full min-h-[24px] max-h-32 bg-transparent text-on-surface placeholder-on-surface-variant resize-none border-none outline-none"
										rows="1"
										onkeydown="handleKeyDown(event)"
										oninput="autoResize(this); updateSendButton();"
									></textarea>
								</div>

								<!-- Action Buttons -->
								<div class="flex items-center gap-2 flex-shrink-0">
									<!-- Voice Input -->
									<button 
										type="button" 
										class="p-2 hover:bg-surface-variant rounded-lg transition-colors"
										id="voice-button" 
										onclick="toggleVoiceInput()"
										title="Voice input"
									>
										<span class="material-symbols-outlined text-on-surface-variant">mic</span>
									</button>

									<!-- Send Button -->
									<button 
										type="submit" 
										id="send-button"
										class="p-2 bg-primary text-on-primary rounded-lg hover:bg-primary/90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
										disabled
									>
										<span class="material-symbols-outlined">send</span>
									</button>
								</div>
							</div>

							<!-- Bottom Bar with Counter and Status -->
							<div class="flex items-center justify-between px-4 pb-3 text-xs text-on-surface-variant">
								<div class="flex items-center gap-4">
									<span>按 Enter 發送，Shift + Enter 換行</span>
								</div>
								<div class="flex items-center gap-2">
									<span id="char-count">0</span>
									<span>/</span>
									<span>2000</span>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
}

// ChatMessage renders a single chat message
templ ChatMessage(message Message, lang string) {
	<div class={
		"flex gap-4 animate-fadeIn",
		templ.KV("justify-end", message.Role == "user"),
		templ.KV("justify-start", message.Role != "user")
	}>
		if message.Role != "user" {
			<!-- AI Avatar -->
			<div class="flex-shrink-0 w-10 h-10 bg-primary-container rounded-full flex items-center justify-center">
				<span class="material-symbols-outlined text-on-primary-container">psychology</span>
			</div>
		}
		
		<!-- Message Content -->
		<div class={
			"max-w-[70%] rounded-extra-large px-4 py-3 shadow-elevation-1 hover-lift transition-all duration-200",
			templ.KV("bg-primary text-on-primary", message.Role == "user"),
			templ.KV("bg-surface-container text-on-surface", message.Role != "user")
		}>
			<div class="space-y-2">
				<!-- Message Text -->
				<div class="md-typescale-body-medium whitespace-pre-wrap">
					{ message.Content }
				</div>
				
				<!-- Metadata (can be used for attachments, etc.) -->
				if message.Metadata != nil && len(message.Metadata) > 0 {
					<div class="space-y-2">
						if attachments, ok := message.Metadata["attachments"].([]interface{}); ok {
							for _, attachment := range attachments {
								<div class="flex items-center gap-2 p-2 bg-surface-variant rounded-medium">
									<span class="material-symbols-outlined text-sm">description</span>
									<span class="text-sm">{ fmt.Sprintf("%v", attachment) }</span>
								</div>
							}
						}
					</div>
				}
				
				<!-- Timestamp -->
				<div class={
					"text-xs opacity-70",
					templ.KV("text-on-primary", message.Role == "user"),
					templ.KV("text-on-surface-variant", message.Role != "user")
				}>
					{ i18n.FormatTime(message.Timestamp, lang) }
				</div>
			</div>
		</div>
		
		if message.Role == "user" {
			<!-- User Avatar -->
			<div class="flex-shrink-0 w-10 h-10 bg-secondary-container rounded-full flex items-center justify-center">
				<span class="material-symbols-outlined text-on-secondary-container">person</span>
			</div>
		}
	</div>
}

// SuggestedPrompt renders a suggested conversation starter
templ SuggestedPrompt(text string, category string) {
	<button 
		class="md3-button md3-button--outlined w-full text-left hover-lift transition-all duration-200"
		onclick="setPrompt(this.dataset.prompt)" data-prompt={text}
	>
		<span class="material-symbols-outlined mr-2">lightbulb</span>
		<span>{ text }</span>
	</button>
}

// ChatAgentCard renders an agent selection card for chat
templ ChatAgentCard(agent Agent, isActive bool, lang string) {
	<div 
		class={
			"md3-card md3-card--outlined p-4 cursor-pointer transition-all duration-200 hover-lift group",
			templ.KV("md3-card--selected ring-2 ring-primary", isActive)
		}
		onclick="selectAgent(this.dataset.agentId)" data-agent-id={agent.ID}
	>
		<div class="flex items-center gap-3 mb-3">
			<div class={
				"w-8 h-8 rounded-medium flex items-center justify-center transition-all duration-200",
				templ.KV("bg-primary text-on-primary", isActive),
				templ.KV("bg-surface-variant text-on-surface-variant group-hover:bg-primary-container group-hover:text-on-primary-container", !isActive)
			}>
				<span class="material-symbols-outlined text-sm">psychology</span>
			</div>
			<div class="flex-1 min-w-0">
				<h4 class="md-typescale-title-small text-on-surface font-medium truncate">{ agent.Name }</h4>
				<div class="flex items-center gap-1">
					<div class="w-1.5 h-1.5 rounded-full bg-primary"></div>
					<span class="md-typescale-body-small text-on-surface-variant">
						{ agent.Status }
					</span>
				</div>
			</div>
		</div>
		<p class="md-typescale-body-small text-on-surface-variant line-clamp-2">{ agent.Description }</p>
		<div class="flex items-center justify-between mt-3">
			<span class="md-typescale-label-small text-on-surface-variant">
				AI Agent
			</span>
			if isActive {
				<span class="md-typescale-label-small text-primary">
					{ i18n.T("status.active", lang) }
				</span>
			}
		</div>
	</div>
}

// ModernChatMessage renders a modern chat message with enhanced design
templ ModernChatMessage(message Message, lang string) {
	<div class={
		"flex gap-4 animate-fadeIn group",
		templ.KV("justify-end", message.Role == "user"),
		templ.KV("justify-start", message.Role != "user")
	}>
		if message.Role != "user" {
			<!-- Modern AI Avatar -->
			<div class="flex-shrink-0 w-9 h-9 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center shadow-sm">
				<span class="material-symbols-outlined text-white text-lg">auto_awesome</span>
			</div>
		}
		
		<!-- Modern Message Bubble -->
		<div class={
			"relative max-w-[75%] rounded-2xl px-4 py-3 shadow-sm transition-all duration-200 group-hover:shadow-md",
			templ.KV("bg-primary text-on-primary rounded-br-md", message.Role == "user"),
			templ.KV("bg-surface-container text-on-surface rounded-bl-md", message.Role != "user")
		}>
			<!-- Message Content with Enhanced Typography -->
			<div class="prose prose-sm max-w-none">
				<div class="text-sm leading-relaxed whitespace-pre-wrap">
					{ message.Content }
				</div>
			</div>
			
			<!-- Enhanced Metadata Display -->
			if message.Metadata != nil && len(message.Metadata) > 0 {
				<div class="mt-3 space-y-2">
					if attachments, ok := message.Metadata["attachments"].([]interface{}); ok {
						for _, attachment := range attachments {
							<div class="flex items-center gap-2 p-2 bg-outline/10 rounded-lg text-xs">
								<span class="material-symbols-outlined text-sm">description</span>
								<span>{ fmt.Sprintf("%v", attachment) }</span>
							</div>
						}
					}
				</div>
			}
			
			<!-- Subtle Timestamp -->
			<div class="mt-2 text-xs opacity-60">
				{ i18n.FormatTime(message.Timestamp, lang) }
			</div>
		</div>
		
		if message.Role == "user" {
			<!-- Modern User Avatar -->
			<div class="flex-shrink-0 w-9 h-9 bg-secondary-container rounded-xl flex items-center justify-center">
				<span class="material-symbols-outlined text-on-secondary-container text-lg">person</span>
			</div>
		}
	</div>
}

// ModernSuggestionCard renders an interactive suggestion card
templ ModernSuggestionCard(title string, icon string, prompt string) {
	<button 
		class="group p-4 bg-surface-container border border-outline-variant rounded-xl hover:bg-primary-container hover:border-primary transition-all duration-200 text-left w-full"
		onclick="setPrompt(this.dataset.prompt)" 
		data-prompt={prompt}
	>
		<div class="flex items-start gap-3">
			<div class="w-8 h-8 bg-primary/10 group-hover:bg-primary group-hover:text-white rounded-lg flex items-center justify-center transition-all duration-200">
				<span class="material-symbols-outlined text-primary group-hover:text-white text-lg">{ icon }</span>
			</div>
			<div class="flex-1">
				<h3 class="font-medium text-on-surface group-hover:text-on-primary-container mb-1">{ title }</h3>
				<p class="text-sm text-on-surface-variant group-hover:text-on-primary-container/80 line-clamp-2">
					{ prompt }
				</p>
			</div>
		</div>
	</button>
}

// ChatHistoryItem renders a chat history item in the sidebar
templ ChatHistoryItem(period string, title string, time string, isActive bool) {
	<div class={
		"group p-3 rounded-lg cursor-pointer transition-all duration-200",
		templ.KV("bg-primary-container text-on-primary-container", isActive),
		templ.KV("hover:bg-surface-variant text-on-surface", !isActive)
	}>
		<div class="flex items-center justify-between mb-1">
			<span class="text-xs font-medium opacity-70">{ period }</span>
			<span class="text-xs opacity-60">{ time }</span>
		</div>
		<div class="font-medium text-sm line-clamp-1">{ title }</div>
		<div class="flex items-center gap-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
			<button class="p-1 hover:bg-surface rounded" title="Pin">
				<span class="material-symbols-outlined text-xs">push_pin</span>
			</button>
			<button class="p-1 hover:bg-error-container rounded text-error" title="Delete">
				<span class="material-symbols-outlined text-xs">delete</span>
			</button>
		</div>
	</div>
}

// CompactAgentCard renders a compact agent selection card
templ CompactAgentCard(agent Agent, isActive bool) {
	<div 
		class={
			"group p-3 rounded-lg cursor-pointer transition-all duration-200 flex items-center gap-3",
			templ.KV("bg-primary-container text-on-primary-container", isActive),
			templ.KV("hover:bg-surface-variant text-on-surface", !isActive)
		}
		onclick="selectAgent(this.dataset.agentId)" 
		data-agent-id={agent.ID}
	>
		<div class={
			"w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0",
			templ.KV("bg-primary text-on-primary", isActive),
			templ.KV("bg-surface-variant text-on-surface-variant", !isActive)
		}>
			<span class="material-symbols-outlined text-sm">psychology</span>
		</div>
		<div class="flex-1 min-w-0">
			<div class="font-medium text-sm line-clamp-1">{ agent.Name }</div>
			<div class="text-xs opacity-70 line-clamp-1">{ agent.Description }</div>
		</div>
		if isActive {
			<div class="w-2 h-2 bg-primary rounded-full flex-shrink-0"></div>
		}
	</div>
}

// ChatPageData for chat page (uses existing types from types.go)
type ChatPageData struct {
	Messages         []Message
	ActiveAgent      Agent
	AvailableAgents  []Agent
	Lang             string
}