package templates

import (
	"fmt"
)

// Modal component with Material Design 3 styling
templ Modal(id string, title string, content templ.Component, actions templ.Component) {
	<div id={id} class="fixed inset-0 z-50 hidden">
		<!-- Backdrop -->
		<div class="fixed inset-0 bg-scrim opacity-32 transition-opacity" onclick="closeModal(event)"></div>
		
		<!-- Modal Dialog -->
		<div class="fixed inset-0 flex items-center justify-center p-4">
			<div class="md3-card md3-card--elevated w-full max-w-md max-h-[90vh] overflow-hidden shadow-elevation-5 transform transition-all scale-95 opacity-0" 
				 id={id + "-dialog"}>
				<!-- Header -->
				<div class="flex items-center justify-between p-6 border-b border-outline-variant">
					<h2 class="md-typescale-headline-small text-on-surface font-medium">{ title }</h2>
					<button class="md3-icon-button" onclick="closeModal(event)">
						<span class="material-symbols-outlined">close</span>
					</button>
				</div>
				
				<!-- Content -->
				<div class="p-6 overflow-y-auto flex-1">
					@content
				</div>
				
				<!-- Actions -->
				if actions != nil {
					<div class="flex items-center justify-end gap-2 p-6 border-t border-outline-variant bg-surface-container-low">
						@actions
					</div>
				}
			</div>
		</div>
	</div>
}

// DataTable component with sorting, filtering, and pagination
templ DataTable(headers []TableHeader, rows []TableRow, config TableConfig) {
	<div class="md3-card md3-card--outlined overflow-hidden">
		<!-- Table Header with Actions -->
		<div class="flex items-center justify-between p-4 bg-surface-container-low border-b border-outline-variant">
			<div class="flex items-center gap-4">
				<h3 class="md-typescale-title-large text-on-surface font-medium">{ config.Title }</h3>
				if config.ShowSearch {
					<div class="relative">
						<input type="text" 
							   placeholder="Search..." 
							   class="md3-textfield w-64 pl-10"
							   hx-get={config.SearchEndpoint}
							   hx-trigger="keyup changed delay:300ms"
							   hx-target="#table-body">
						<span class="absolute left-3 top-1/2 transform -translate-y-1/2 material-symbols-outlined text-on-surface-variant">search</span>
					</div>
				}
			</div>
			<div class="flex items-center gap-2">
				if config.ShowFilter {
					<button class="md3-button md3-button--outlined" onclick="showFilterModal()">
						<span class="material-symbols-outlined">filter_list</span>
						<span class="ml-2">Filter</span>
					</button>
				}
				if config.ShowExport {
					<button class="md3-button md3-button--outlined" onclick="exportData()">
						<span class="material-symbols-outlined">download</span>
						<span class="ml-2">Export</span>
					</button>
				}
				if config.ShowAdd {
					<button class="md3-button md3-button--filled" onclick="showAddModal()">
						<span class="material-symbols-outlined">add</span>
						<span class="ml-2">Add New</span>
					</button>
				}
			</div>
		</div>
		
		<!-- Table -->
		<div class="overflow-x-auto">
			<table class="w-full">
				<thead class="bg-surface-container">
					<tr>
						if config.ShowSelection {
							<th class="p-4 text-left">
								<input type="checkbox" class="md3-checkbox" onchange="toggleAllRows(this)">
							</th>
						}
						for _, header := range headers {
							<th class="p-4 text-left group cursor-pointer hover:bg-surface-variant transition-standard"
								onclick="sortTable(this.dataset.key)" data-key={header.Key}>
								<div class="flex items-center gap-2">
									<span class="md-typescale-title-small text-on-surface font-medium">{ header.Label }</span>
									if header.Sortable {
										<span class="material-symbols-outlined text-on-surface-variant group-hover:text-on-surface transition-standard">
											unfold_more
										</span>
									}
								</div>
							</th>
						}
						<th class="p-4 text-right">
							<span class="md-typescale-title-small text-on-surface font-medium">Actions</span>
						</th>
					</tr>
				</thead>
				<tbody id="table-body" class="divide-y divide-outline-variant">
					for i, row := range rows {
						@TableRowComponent(row, config, i%2 == 0)
					}
				</tbody>
			</table>
		</div>
		
		<!-- Pagination -->
		if config.ShowPagination {
			<div class="flex items-center justify-between p-4 bg-surface-container-low border-t border-outline-variant">
				<div class="flex items-center gap-2">
					<span class="md-typescale-body-medium text-on-surface-variant">Rows per page:</span>
					<select class="bg-surface-variant text-on-surface-variant rounded-medium px-2 py-1 border border-outline">
						<option value="10">10</option>
						<option value="25">25</option>
						<option value="50">50</option>
						<option value="100">100</option>
					</select>
				</div>
				<div class="flex items-center gap-4">
					<span class="md-typescale-body-medium text-on-surface-variant">
						{ fmt.Sprintf("%d-%d of %d", config.CurrentPage*config.PageSize+1, (config.CurrentPage+1)*config.PageSize, config.TotalRows) }
					</span>
					<div class="flex items-center gap-1">
						<button class="md3-icon-button" 
								disabled={ config.CurrentPage == 0 }
								onclick="navigatePage(this.dataset.page)" data-page="0">
							<span class="material-symbols-outlined">first_page</span>
						</button>
						<button class="md3-icon-button" 
								disabled={ config.CurrentPage == 0 }
								onclick="navigatePage(this.dataset.page)" data-page={fmt.Sprintf("%d", config.CurrentPage-1)}>
							<span class="material-symbols-outlined">chevron_left</span>
						</button>
						<button class="md3-icon-button" 
								disabled={ config.CurrentPage >= config.TotalPages-1 }
								onclick="navigatePage(this.dataset.page)" data-page={fmt.Sprintf("%d", config.CurrentPage+1)}>
							<span class="material-symbols-outlined">chevron_right</span>
						</button>
						<button class="md3-icon-button" 
								disabled={ config.CurrentPage >= config.TotalPages-1 }
								onclick="navigatePage(this.dataset.page)" data-page={fmt.Sprintf("%d", config.TotalPages-1)}>
							<span class="material-symbols-outlined">last_page</span>
						</button>
					</div>
				</div>
			</div>
		}
	</div>
}

// TableRowComponent renders individual table rows
templ TableRowComponent(row TableRow, config TableConfig, isEven bool) {
	<tr class={
		"hover:bg-surface-variant transition-standard cursor-pointer",
		templ.KV("bg-surface-container-low", isEven)
	} onclick="selectRow(this.dataset.rowId)" data-row-id={row.ID}>
		if config.ShowSelection {
			<td class="p-4">
				<input type="checkbox" class="md3-checkbox" value={row.ID}>
			</td>
		}
		for _, cell := range row.Cells {
			<td class="p-4">
				if cell.Type == "text" {
					<span class="md-typescale-body-medium text-on-surface">{ cell.Value }</span>
				} else if cell.Type == "badge" {
					<span class={
						"md-typescale-label-small px-2 py-1 rounded-full",
						templ.KV("bg-primary-container text-on-primary-container", cell.Variant == "primary"),
						templ.KV("bg-secondary-container text-on-secondary-container", cell.Variant == "secondary"),
						templ.KV("bg-error-container text-on-error-container", cell.Variant == "error"),
						templ.KV("bg-surface-variant text-on-surface-variant", cell.Variant == "default")
					}>
						{ cell.Value }
					</span>
				} else if cell.Type == "icon" {
					<span class="material-symbols-outlined text-on-surface-variant">{ cell.Value }</span>
				} else if cell.Type == "avatar" {
					<div class="w-8 h-8 bg-primary-container rounded-full flex items-center justify-center">
						<span class="md-typescale-label-medium text-on-primary-container">
							{ cell.Value }
						</span>
					</div>
				}
			</td>
		}
		<td class="p-4 text-right">
			<div class="flex items-center justify-end gap-1">
				<button class="md3-icon-button" onclick="editRow(this.dataset.rowId)" data-row-id={row.ID}>
					<span class="material-symbols-outlined">edit</span>
				</button>
				<button class="md3-icon-button text-error" onclick="deleteRow(this.dataset.rowId)" data-row-id={row.ID}>
					<span class="material-symbols-outlined">delete</span>
				</button>
				<button class="md3-icon-button" onclick="showRowMenu(this.dataset.rowId)" data-row-id={row.ID}>
					<span class="material-symbols-outlined">more_vert</span>
				</button>
			</div>
		</td>
	</tr>
}

// FormField renders form inputs with Material Design 3 styling
templ FormField(field FormFieldConfig) {
	<div class="space-y-2">
		if field.Label != "" {
			<label class="md-typescale-body-small text-on-surface font-medium">
				{ field.Label }
				if field.Required {
					<span class="text-error">*</span>
				}
			</label>
		}
		
		if field.Type == "text" || field.Type == "email" || field.Type == "password" {
			<input 
				type={ field.Type }
				name={ field.Name }
				placeholder={ field.Placeholder }
				value={ field.Value }
				required?={ field.Required }
				class="md3-textfield w-full"
			>
		} else if field.Type == "textarea" {
			<textarea 
				name={ field.Name }
				placeholder={ field.Placeholder }
				required?={ field.Required }
				rows="4"
				class="md3-textfield w-full resize-vertical"
			>{ field.Value }</textarea>
		} else if field.Type == "select" {
			<select name={ field.Name } required?={ field.Required } class="md3-select w-full">
				if field.Placeholder != "" {
					<option value="">{ field.Placeholder }</option>
				}
				for _, option := range field.Options {
					<option value={ option.Value } selected?={ option.Value == field.Value }>
						{ option.Label }
					</option>
				}
			</select>
		} else if field.Type == "checkbox" {
			<label class="flex items-center gap-3 cursor-pointer">
				<input 
					type="checkbox" 
					name={ field.Name }
					value={ field.Value }
					checked?={ field.Value == "true" }
					class="md3-checkbox"
				>
				<span class="md-typescale-body-medium text-on-surface">{ field.Placeholder }</span>
			</label>
		} else if field.Type == "radio" {
			<div class="space-y-2">
				for _, option := range field.Options {
					<label class="flex items-center gap-3 cursor-pointer">
						<input 
							type="radio" 
							name={ field.Name }
							value={ option.Value }
							checked?={ option.Value == field.Value }
							class="md3-radio"
						>
						<span class="md-typescale-body-medium text-on-surface">{ option.Label }</span>
					</label>
				}
			</div>
		} else if field.Type == "file" {
			<div class="relative">
				<input 
					type="file"
					name={ field.Name }
					accept={ field.Accept }
					required?={ field.Required }
					class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
					onchange="updateFileLabel(this)"
				>
				<div class="md3-textfield flex items-center justify-between cursor-pointer">
					<span class="text-on-surface-variant" id={field.Name + "-label"}>
						{ field.Placeholder }
					</span>
					<span class="material-symbols-outlined text-on-surface-variant">upload</span>
				</div>
			</div>
		}
		
		if field.HelpText != "" {
			<p class="md-typescale-body-small text-on-surface-variant">{ field.HelpText }</p>
		}
		
		if field.ErrorText != "" {
			<p class="md-typescale-body-small text-error flex items-center gap-1">
				<span class="material-symbols-outlined text-sm">error</span>
				{ field.ErrorText }
			</p>
		}
	</div>
}

// NotificationToast for displaying messages
templ NotificationToast(id string, message string, variant string, duration int) {
	<div id={id} 
		 class={
		 	"fixed top-4 right-4 z-50 md3-card md3-card--elevated p-4 shadow-elevation-3 transform translate-x-full transition-transform",
		 	templ.KV("bg-primary-container", variant == "success"),
		 	templ.KV("bg-error-container", variant == "error"),
		 	templ.KV("bg-secondary-container", variant == "warning"),
		 	templ.KV("bg-surface-container", variant == "info")
		 }>
		<div class="flex items-center gap-3">
			<span class={
				"material-symbols-outlined",
				templ.KV("text-on-primary-container", variant == "success"),
				templ.KV("text-on-error-container", variant == "error"),
				templ.KV("text-on-secondary-container", variant == "warning"),
				templ.KV("text-on-surface", variant == "info")
			}>
				if variant == "success" {
					check_circle
				} else if variant == "error" {
					error
				} else if variant == "warning" {
					warning
				} else {
					info
				}
			</span>
			<span class={
				"md-typescale-body-medium",
				templ.KV("text-on-primary-container", variant == "success"),
				templ.KV("text-on-error-container", variant == "error"),
				templ.KV("text-on-secondary-container", variant == "warning"),
				templ.KV("text-on-surface", variant == "info")
			}>
				{ message }
			</span>
			<button class={
				"md3-icon-button",
				templ.KV("text-on-primary-container", variant == "success"),
				templ.KV("text-on-error-container", variant == "error"),
				templ.KV("text-on-secondary-container", variant == "warning"),
				templ.KV("text-on-surface", variant == "info")
			} onclick="hideToast(this.dataset.toastId)" data-toast-id={id}>
				<span class="material-symbols-outlined">close</span>
			</button>
		</div>
	</div>
}

// ProgressIndicator for loading states
templ ProgressIndicator(label string, progress int, showPercentage bool) {
	<div class="space-y-3">
		<div class="flex items-center justify-between">
			<span class="md-typescale-body-medium text-on-surface">{ label }</span>
			if showPercentage {
				<span class="md-typescale-body-small text-on-surface-variant">{ fmt.Sprintf("%d%%", progress) }</span>
			}
		</div>
		<div class="w-full bg-surface-variant rounded-full h-1.5 overflow-hidden">
			<div 
				class="h-full bg-primary rounded-full transition-all duration-500 ease-out"
				style={ fmt.Sprintf("width: %d%%", progress) }
			></div>
		</div>
	</div>
}

// StatsCard for displaying statistics
templ StatsCard(title string, value string, change string, trend string, icon string) {
	<div class="md3-card md3-card--filled">
		<div class="p-4">
			<div class="flex items-start justify-between mb-3">
				<div class="flex-1">
					<h4 class="md-typescale-label-large text-on-surface-variant mb-1">{ title }</h4>
					<p class="md-typescale-headline-medium text-on-surface font-medium">{ value }</p>
				</div>
				<div class="w-10 h-10 bg-primary-container rounded-large flex items-center justify-center">
					<span class="material-symbols-outlined text-on-primary-container">{ icon }</span>
				</div>
			</div>
			if change != "" {
				<div class="flex items-center gap-1">
					<span class={
						"material-symbols-outlined text-sm",
						templ.KV("text-primary", trend == "up"),
						templ.KV("text-error", trend == "down"),
						templ.KV("text-on-surface-variant", trend == "neutral")
					}>
						if trend == "up" {
							trending_up
						} else if trend == "down" {
							trending_down
						} else {
							trending_flat
						}
					</span>
					<span class={
						"md-typescale-body-small",
						templ.KV("text-primary", trend == "up"),
						templ.KV("text-error", trend == "down"),
						templ.KV("text-on-surface-variant", trend == "neutral")
					}>
						{ change }
					</span>
				</div>
			}
		</div>
	</div>
}

// Type definitions for the components
type TableHeader struct {
	Key      string
	Label    string
	Sortable bool
}

type TableRow struct {
	ID    string
	Cells []TableCell
}

type TableCell struct {
	Type    string // text, badge, icon, avatar
	Value   string
	Variant string // primary, secondary, error, default
}

type TableConfig struct {
	Title          string
	ShowSearch     bool
	ShowFilter     bool
	ShowExport     bool
	ShowAdd        bool
	ShowSelection  bool
	ShowPagination bool
	SearchEndpoint string
	CurrentPage    int
	PageSize       int
	TotalRows      int
	TotalPages     int
}

type FormFieldConfig struct {
	Type        string
	Name        string
	Label       string
	Placeholder string
	Value       string
	Required    bool
	HelpText    string
	ErrorText   string
	Accept      string // for file inputs
	Options     []FormOption
}

type FormOption struct {
	Value string
	Label string
}