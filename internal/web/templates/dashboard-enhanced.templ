package templates

import (
	"fmt"
	"time"
	"github.com/koopa0/assistant-go/internal/web/i18n"
)

// EnhancedDashboard implements a professional MD3 dashboard with rich information architecture
templ EnhancedDashboard(data DashboardData) {
	<div class="min-h-full bg-background">
		<!-- Hero Section with Key Metrics -->
		<section class="px-6 py-8 bg-surface-container-low border-b border-outline-variant">
			<div class="max-w-7xl mx-auto">
				<!-- Welcome Header -->
				<div class="mb-8">
					<h1 class="md-typescale-display-small text-on-surface mb-2">
						{ i18n.T("dashboard.welcome", data.Lang) }
					</h1>
					<p class="md-typescale-body-large text-on-surface-variant">
						{ i18n.FormatDateTime(time.Now(), data.Lang) } • { i18n.T("dashboard.overview", data.Lang) }
					</p>
				</div>

				<!-- Key Performance Indicators -->
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
					@EnhancedMetricCard(
						"AI Agents Active", 
						fmt.Sprintf("%d", data.ActiveAgents), 
						"+2 from yesterday",
						"smart_toy", 
						"primary",
						"",
					)
					@EnhancedMetricCard(
						"Tasks Completed", 
						fmt.Sprintf("%d", data.TasksCompleted), 
						"+12% from last week",
						"task_alt", 
						"secondary",
						"trend-up",
					)
					@EnhancedMetricCard(
						"System Health", 
						data.SystemHealth, 
						"All systems operational",
						"health_and_safety", 
						"tertiary",
						"",
					)
					@EnhancedMetricCard(
						"Uptime", 
						formatDuration(data.Uptime), 
						"Continuous operation",
						"schedule", 
						"primary",
						"",
					)
				</div>
			</div>
		</section>

		<!-- Main Dashboard Content -->
		<div class="max-w-7xl mx-auto px-6 py-8">
			<div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
				<!-- Left Column: Main Content -->
				<div class="lg:col-span-8 space-y-8">
					<!-- AI Agents Status Section -->
					<section>
						<div class="flex items-center justify-between mb-6">
							<div>
								<h2 class="md-typescale-headline-small text-on-surface mb-1">
									{ i18n.T("dashboard.ai_agents", data.Lang) }
								</h2>
								<p class="md-typescale-body-medium text-on-surface-variant">
									{ i18n.T("dashboard.agents_description", data.Lang) }
								</p>
							</div>
							<button class="md3-button md3-button--outlined">
								<span class="material-symbols-outlined">add</span>
								<span class="ml-2">Add Agent</span>
							</button>
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							for _, agent := range data.AgentStatuses {
								@EnhancedAgentCard(agent, data.Lang)
							}
						</div>
					</section>

					<!-- Performance Analytics -->  
					<section>
						<div class="flex items-center justify-between mb-6">
							<div>
								<h2 class="md-typescale-headline-small text-on-surface mb-1">
									Performance Analytics
								</h2>
								<p class="md-typescale-body-medium text-on-surface-variant">
									Real-time system performance and usage metrics
								</p>
							</div>
							<div class="flex items-center gap-2">
								<select class="md3-select bg-surface-variant text-on-surface-variant rounded-medium px-3 py-2 border border-outline" 
										hx-get="/api/analytics" 
										hx-trigger="change"
										hx-target="#analytics-container">
									<option value="24h">Last 24 hours</option>
									<option value="7d">Last 7 days</option>
									<option value="30d">Last 30 days</option>
								</select>
								<button class="md3-icon-button" 
										hx-get="/api/analytics/refresh" 
										hx-target="#analytics-container"
										aria-label="Refresh analytics data">
									<span class="material-symbols-outlined">refresh</span>
								</button>
							</div>
						</div>

						<div id="analytics-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							@InteractiveChart("CPU Usage", 85, "Cores: 8", "processor", []ChartData{{"00:00", 45}, {"06:00", 62}, {"12:00", 85}, {"18:00", 74}})
							@InteractiveChart("Memory Usage", 67, "Total: 16GB", "memory", []ChartData{{"00:00", 32}, {"06:00", 45}, {"12:00", 67}, {"18:00", 58}})
							@InteractiveChart("Network I/O", 43, "Bandwidth: 1Gbps", "network_check", []ChartData{{"00:00", 12}, {"06:00", 28}, {"12:00", 43}, {"18:00", 35}})
						</div>
					</section>

					<!-- Recent Conversations -->
					<section>
						<div class="flex items-center justify-between mb-6">
							<h2 class="md-typescale-headline-small text-on-surface">
								{ i18n.T("dashboard.recent_chats", data.Lang) }
							</h2>
							<a href="/chat" class="md3-button md3-button--text" hx-boost="true">
								<span>View All</span>
								<span class="material-symbols-outlined ml-2">arrow_forward</span>
							</a>
						</div>

						<div class="md3-card md3-card--outlined">
							<div class="divide-y divide-outline-variant">
								for i, chat := range data.RecentChats {
									@EnhancedChatItem(chat, data.Lang, i == 0)
								}
							</div>
							<div class="p-4 bg-surface-container-low">
								<button class="md3-button md3-button--filled w-full">
									<span class="material-symbols-outlined">add</span>
									<span class="ml-2">{ i18n.T("chat.new_conversation", data.Lang) }</span>
								</button>
							</div>
						</div>
					</section>
				</div>

				<!-- Right Column: Sidebar -->
				<div class="lg:col-span-4 space-y-6">
					<!-- Real-time Activity Feed -->
					<section>
						<h3 class="md-typescale-title-large text-on-surface mb-4">
							{ i18n.T("dashboard.live_activity", data.Lang) }
						</h3>
						<div class="md3-card md3-card--elevated">
							<div class="p-4">
								<div class="flex items-center gap-2 mb-4">
									<div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
									<span class="md-typescale-label-small text-on-surface-variant uppercase tracking-wide">
										Live Updates
									</span>
								</div>
								<div class="space-y-3 max-h-96 overflow-y-auto" 
									 hx-get="/api/activities" 
									 hx-trigger="load, every 10s"
									 hx-swap="innerHTML">
									for _, activity := range data.RecentActivities {
										@EnhancedActivityItem(activity, data.Lang)
									}
								</div>
							</div>
						</div>
					</section>

					<!-- System Status -->
					<section>
						<h3 class="md-typescale-title-large text-on-surface mb-4">
							System Status
						</h3>
						<div class="md3-card md3-card--filled">
							<div class="p-4 space-y-4">
								@StatusIndicator("API Gateway", "Operational", "check_circle", "primary")
								@StatusIndicator("Database", "Healthy", "storage", "primary") 
								@StatusIndicator("AI Services", "Active", "psychology", "primary")
								@StatusIndicator("Background Jobs", "Processing", "pending", "secondary")
							</div>
						</div>
					</section>

					<!-- Quick Actions -->
					<section>
						<h3 class="md-typescale-title-large text-on-surface mb-4">
							Quick Actions
						</h3>
						<div class="space-y-3">
							@QuickActionButton("Start New Chat", "chat", "/chat")
							@QuickActionButton("Run Diagnostic", "troubleshoot", "/diagnostic")
							@QuickActionButton("View Logs", "description", "/logs")
							@QuickActionButton("Export Data", "download", "/export")
						</div>
					</section>

					<!-- Resource Usage -->
					<section>
						<h3 class="md-typescale-title-large text-on-surface mb-4">
							Resource Usage
						</h3>
						<div class="md3-card md3-card--outlined">
							<div class="p-4 space-y-4">
								@ResourceMeter("CPU", 75, "processor")
								@ResourceMeter("Memory", 68, "memory")
								@ResourceMeter("Storage", 45, "hard_drive")
								@ResourceMeter("Network", 23, "network_check")
							</div>
						</div>
					</section>
				</div>
			</div>
		</div>

		<!-- Floating Action Button -->
		<div class="fixed bottom-6 right-6 z-10">
			<button 
				class="md3-fab md3-fab--extended md3-state-layer shadow-elevation-3"
				aria-label={ i18n.T("chat.new_conversation", data.Lang) }
				hx-get="/chat/new"
				hx-boost="true"
			>
				<span class="material-symbols-outlined">add</span>
				<span class="ml-2 md-typescale-label-large">New Chat</span>
			</button>
		</div>
	</div>
}

// EnhancedMetricCard displays key performance indicators
templ EnhancedMetricCard(title string, value string, subtitle string, icon string, variant string, trend string) {
	<div class="md3-card md3-card--elevated md3-state-layer group cursor-pointer transition-emphasized">
		<div class="p-6">
			<div class="flex items-start justify-between mb-4">
				<div class="flex-1 min-w-0">
					<h3 class="md-typescale-label-large text-on-surface-variant mb-1">{ title }</h3>
					<p class="md-typescale-headline-medium text-on-surface font-medium">{ value }</p>
				</div>
				<div class={ 
					"w-12 h-12 rounded-large flex items-center justify-center flex-shrink-0 transition-emphasized group-hover:scale-110",
					templ.KV("bg-primary-container", variant == "primary"),
					templ.KV("bg-secondary-container", variant == "secondary"),
					templ.KV("bg-tertiary-container", variant == "tertiary")
				}>
					<span class={
						"material-symbols-outlined",
						templ.KV("text-on-primary-container", variant == "primary"),
						templ.KV("text-on-secondary-container", variant == "secondary"),
						templ.KV("text-on-tertiary-container", variant == "tertiary")
					}>{ icon }</span>
				</div>
			</div>
			if subtitle != "" {
				<div class="flex items-center gap-2">
					if trend == "trend-up" {
						<span class="material-symbols-outlined text-primary text-sm">trending_up</span>
					}
					<p class="md-typescale-body-small text-on-surface-variant">{ subtitle }</p>
				</div>
			}
		</div>
	</div>
}

// EnhancedAgentCard displays agent status with rich information
templ EnhancedAgentCard(agent AgentStatus, lang string) {
	<div class="md3-card md3-card--outlined md3-state-layer group cursor-pointer transition-emphasized"
		 hx-get={ agent.Href } 
		 hx-boost="true">
		<div class="p-4">
			<div class="flex items-start gap-4">
				<div class="flex-shrink-0">
					<div class={
						"w-12 h-12 rounded-large flex items-center justify-center transition-emphasized",
						templ.KV("bg-primary-container", agent.IsActive),
						templ.KV("bg-surface-variant", !agent.IsActive)
					}>
						<span class={
							"material-symbols-outlined",
							templ.KV("text-on-primary-container", agent.IsActive),
							templ.KV("text-on-surface-variant", !agent.IsActive)
						}>psychology</span>
					</div>
				</div>
				<div class="flex-1 min-w-0">
					<div class="flex items-center gap-2 mb-1">
						<h4 class="md-typescale-title-medium text-on-surface font-medium truncate">{ agent.Name }</h4>
						<div class={
							"w-2 h-2 rounded-full flex-shrink-0",
							templ.KV("bg-primary animate-pulse", agent.IsActive),
							templ.KV("bg-outline", !agent.IsActive)
						}></div>
					</div>
					<p class="md-typescale-body-small text-on-surface-variant mb-2 line-clamp-2">{ agent.Description }</p>
					<div class="flex items-center justify-between">
						<span class={
							"md-typescale-label-small px-2 py-1 rounded-small",
							templ.KV("bg-primary-container text-on-primary-container", agent.IsActive),
							templ.KV("bg-surface-variant text-on-surface-variant", !agent.IsActive)
						}>
							{ agent.Status }
						</span>
						<span class="md-typescale-body-small text-on-surface-variant">
							{ i18n.FormatTime(agent.LastUsed, lang) }
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
}

// EnhancedActivityItem displays a real-time activity with rich context
templ EnhancedActivityItem(activity Activity, lang string) {
	<div class="flex items-start gap-3 p-2 rounded-medium hover:bg-surface-variant transition-standard group">
		<div class={
			"w-8 h-8 rounded-large flex items-center justify-center flex-shrink-0 transition-emphasized group-hover:scale-110",
			templ.KV("bg-primary-container", activity.Status == "success"),
			templ.KV("bg-error-container", activity.Status == "error"),
			templ.KV("bg-surface-variant", activity.Status == "pending")
		}>
			<span class={
				"material-symbols-outlined text-sm",
				templ.KV("text-on-primary-container", activity.Status == "success"),
				templ.KV("text-on-error-container", activity.Status == "error"),
				templ.KV("text-on-surface-variant", activity.Status == "pending")
			}>
				{ activity.Icon }
			</span>
		</div>
		<div class="flex-1 min-w-0">
			<p class="md-typescale-body-medium text-on-surface">{ activity.Description }</p>
			<div class="flex items-center gap-2 mt-1">
				<p class="md-typescale-body-small text-on-surface-variant">
					{ i18n.FormatTime(activity.Timestamp, lang) }
				</p>
				<span class="w-1 h-1 bg-outline rounded-full"></span>
				<span class={
					"md-typescale-label-small uppercase tracking-wide",
					templ.KV("text-primary", activity.Status == "success"),
					templ.KV("text-error", activity.Status == "error"),
					templ.KV("text-secondary", activity.Status == "pending")
				}>
					{ activity.Type }
				</span>
			</div>
		</div>
	</div>
}

// EnhancedChatItem displays recent chat conversations
templ EnhancedChatItem(chat ChatSummary, lang string, isLatest bool) {
	<div class="p-4 hover:bg-surface-variant transition-standard cursor-pointer group"
		 hx-get={ "/chat/" + chat.ID }
		 hx-boost="true">
		<div class="flex items-start gap-4">
			<div class="flex-shrink-0">
				<div class="w-10 h-10 bg-secondary-container rounded-large flex items-center justify-center">
					<span class="material-symbols-outlined text-on-secondary-container text-lg">chat</span>
				</div>
			</div>
			<div class="flex-1 min-w-0">
				<div class="flex items-center gap-2 mb-1">
					<h4 class="md-typescale-title-small text-on-surface font-medium truncate">{ chat.Title }</h4>
					if isLatest {
						<span class="md-typescale-label-small bg-primary text-on-primary px-2 py-0.5 rounded-small">Latest</span>
					}
				</div>
				<p class="md-typescale-body-small text-secondary mb-1">{ chat.Agent }</p>
				<p class="md-typescale-body-small text-on-surface-variant line-clamp-2 mb-2">{ chat.Preview }</p>
				<div class="flex items-center justify-between">
					<span class="md-typescale-body-small text-on-surface-variant">
						{ i18n.FormatTime(chat.Timestamp, lang) }
					</span>
					<span class="material-symbols-outlined text-on-surface-variant group-hover:text-on-surface transition-standard">
						arrow_forward
					</span>
				</div>
			</div>
		</div>
	</div>
}

// ChartData represents a data point for charts
type ChartData struct {
	Label string
	Value int
}

// InteractiveChart displays analytics with visual data representation
templ InteractiveChart(title string, currentValue int, subtitle string, icon string, data []ChartData) {
	<div class="md3-card md3-card--elevated md3-state-layer group cursor-pointer transition-emphasized" 
		 hx-get={ "/api/chart/" + title } 
		 hx-trigger="click"
		 hx-target="#chart-modal"
		 hx-swap="innerHTML">
		<div class="p-4">
			<div class="flex items-start justify-between mb-4">
				<div class="flex items-center gap-3">
					<div class="w-10 h-10 bg-primary-container rounded-large flex items-center justify-center">
						<span class="material-symbols-outlined text-on-primary-container">{ icon }</span>
					</div>
					<div>
						<h4 class="md-typescale-title-medium text-on-surface font-medium">{ title }</h4>
						<p class="md-typescale-body-small text-on-surface-variant">{ subtitle }</p>
					</div>
				</div>
				<div class="text-right">
					<span class="md-typescale-headline-small text-primary font-medium">{ fmt.Sprintf("%d%%", currentValue) }</span>
					<div class="flex items-center gap-1 mt-1 justify-end">
						if currentValue > 70 {
							<span class="material-symbols-outlined text-error text-sm">trending_up</span>
							<span class="md-typescale-label-small text-error">High</span>
						} else if currentValue > 50 {
							<span class="material-symbols-outlined text-secondary text-sm">trending_up</span>
							<span class="md-typescale-label-small text-secondary">Medium</span>
						} else {
							<span class="material-symbols-outlined text-primary text-sm">trending_down</span>
							<span class="md-typescale-label-small text-primary">Normal</span>
						}
					</div>
				</div>
			</div>
			
			<!-- Mini Chart Visualization -->
			<div class="relative h-16 mb-4">
				<div class="flex items-end justify-between h-full gap-1">
					for _, point := range data {
						<div class="relative group flex-1">
							<div 
								class="bg-primary-container rounded-t-small transition-all duration-300 hover:bg-primary group-hover:opacity-80"
								style={ fmt.Sprintf("height: %d%%", (point.Value * 100) / 100) }
							></div>
							<!-- Tooltip -->
							<div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity">
								<div class="bg-surface-container text-on-surface px-2 py-1 rounded-medium text-xs whitespace-nowrap shadow-elevation-2">
									{ point.Label }: { fmt.Sprintf("%d%%", point.Value) }
								</div>
							</div>
						</div>
					}
				</div>
			</div>
			
			<!-- Progress Indicator -->
			<div class="w-full bg-surface-variant rounded-full h-2">
				<div 
					class={
						"h-2 rounded-full transition-all duration-1000 ease-out",
						templ.KV("bg-primary", currentValue < 70),
						templ.KV("bg-secondary", currentValue >= 70 && currentValue < 85),
						templ.KV("bg-error", currentValue >= 85)
					}
					style={ fmt.Sprintf("width: %d%%", currentValue) }
				></div>
			</div>
			
			<div class="flex items-center justify-between mt-3 text-xs">
				for idx, point := range data {
					if idx == 0 || idx == len(data)-1 {
						<span class="md-typescale-label-small text-on-surface-variant">{ point.Label }</span>
					}
				}
			</div>
		</div>
	</div>
}

// StatusIndicator shows system component status
templ StatusIndicator(service string, status string, icon string, variant string) {
	<div class="flex items-center justify-between p-2 rounded-medium hover:bg-surface-variant transition-standard">
		<div class="flex items-center gap-3">
			<span class={
				"material-symbols-outlined",
				templ.KV("text-primary", variant == "primary"),
				templ.KV("text-secondary", variant == "secondary")
			}>{ icon }</span>
			<span class="md-typescale-body-medium text-on-surface">{ service }</span>
		</div>
		<span class={
			"md-typescale-label-small px-2 py-1 rounded-small",
			templ.KV("bg-primary-container text-on-primary-container", variant == "primary"),
			templ.KV("bg-secondary-container text-on-secondary-container", variant == "secondary")
		}>
			{ status }
		</span>
	</div>
}

// QuickActionButton provides quick access to common actions
templ QuickActionButton(label string, icon string, href string) {
	<a href={ templ.URL(href) } 
	   class="md3-button md3-button--outlined w-full justify-start" 
	   hx-boost="true">
		<span class="material-symbols-outlined">{ icon }</span>
		<span class="ml-2">{ label }</span>
	</a>
}

// ResourceMeter displays resource usage with visual indicator
templ ResourceMeter(name string, percentage int, icon string) {
	<div class="space-y-2">
		<div class="flex items-center justify-between">
			<div class="flex items-center gap-2">
				<span class="material-symbols-outlined text-on-surface-variant text-sm">{ icon }</span>
				<span class="md-typescale-body-small text-on-surface">{ name }</span>
			</div>
			<span class="md-typescale-label-small text-on-surface-variant">{ fmt.Sprintf("%d%%", percentage) }</span>
		</div>
		<div class="w-full bg-surface-variant rounded-full h-1.5">
			<div 
				class={
					"h-1.5 rounded-full transition-all duration-500",
					templ.KV("bg-primary", percentage < 70),
					templ.KV("bg-secondary", percentage >= 70 && percentage < 85),
					templ.KV("bg-error", percentage >= 85)
				}
				style={ fmt.Sprintf("width: %d%%", percentage) }
			></div>
		</div>
	</div>
}