package templates

import "fmt"

// InputVariant defines the input field style variants
type InputVariant string

const (
	InputFilled   InputVariant = "filled"
	InputOutlined InputVariant = "outlined"
)

// TextField implements Material Design 3 text field
templ TextField(variant InputVariant, label string, placeholder string, helperText string, errorText string, required bool, disabled bool, attrs templ.Attributes) {
	<div class="relative">
		<div class={
			"relative",
			getTextFieldContainerClasses(variant, errorText != "", disabled)
		}>
			<input 
				{ attrs... }
				placeholder={ placeholder }
				required?={ required }
				disabled?={ disabled }
				class={
					"w-full bg-transparent outline-none transition-standard peer",
					getTextFieldInputClasses(variant)
				}
			/>
			if label != "" {
				<label class={
					"absolute left-4 transition-all duration-200 pointer-events-none peer-placeholder-shown:top-4 peer-placeholder-shown:text-body-large peer-focus:top-2 peer-focus:text-label-small",
					templ.KV("top-2 text-label-small", placeholder != ""),
					templ.KV("text-primary", errorText == ""),
					templ.KV("text-error", errorText != ""),
					templ.KV("text-on-surface-variant", disabled)
				}>
					{ label }
					if required {
						<span class="text-error ml-1">*</span>
					}
				</label>
			}
		</div>
		if helperText != "" || errorText != "" {
			<div class="mt-1 px-4">
				if errorText != "" {
					<p class="text-body-small text-error flex items-center">
						<span class="material-symbols-outlined text-sm mr-1">error</span>
						{ errorText }
					</p>
				} else {
					<p class="text-body-small text-on-surface-variant">{ helperText }</p>
				}
			</div>
		}
	</div>
}

// TextArea implements Material Design 3 text area
templ TextArea(variant InputVariant, label string, placeholder string, rows int, helperText string, errorText string, required bool, disabled bool, attrs templ.Attributes) {
	<div class="relative">
		<div class={
			"relative",
			getTextFieldContainerClasses(variant, errorText != "", disabled)
		}>
			<textarea 
				{ attrs... }
				placeholder={ placeholder }
				required?={ required }
				disabled?={ disabled }
				rows={ fmt.Sprintf("%d", rows) }
				class={
					"w-full bg-transparent outline-none transition-standard peer resize-none",
					getTextFieldInputClasses(variant)
				}
			></textarea>
			if label != "" {
				<label class={
					"absolute left-4 top-2 text-label-small transition-all duration-200 pointer-events-none",
					templ.KV("text-primary", errorText == ""),
					templ.KV("text-error", errorText != ""),
					templ.KV("text-on-surface-variant", disabled)
				}>
					{ label }
					if required {
						<span class="text-error ml-1">*</span>
					}
				</label>
			}
		</div>
		if helperText != "" || errorText != "" {
			<div class="mt-1 px-4">
				if errorText != "" {
					<p class="text-body-small text-error flex items-center">
						<span class="material-symbols-outlined text-sm mr-1">error</span>
						{ errorText }
					</p>
				} else {
					<p class="text-body-small text-on-surface-variant">{ helperText }</p>
				}
			</div>
		}
	</div>
}

// SearchField implements Material Design 3 search field
templ SearchField(placeholder string, value string, attrs templ.Attributes) {
	<div class="relative">
		<div class="relative bg-surface-container-high rounded-extra-large">
			<div class="absolute left-4 top-1/2 transform -translate-y-1/2">
				<span class="material-symbols-outlined text-on-surface-variant">search</span>
			</div>
			<input 
				{ attrs... }
				type="search"
				placeholder={ placeholder }
				value={ value }
				class="w-full pl-12 pr-4 py-3 bg-transparent outline-none text-body-large text-on-surface placeholder-on-surface-variant rounded-extra-large focus:bg-surface-container-highest transition-standard"
			/>
		</div>
	</div>
}

// Select implements Material Design 3 select field
templ Select(variant InputVariant, label string, options []SelectOption, selected string, helperText string, errorText string, required bool, disabled bool, attrs templ.Attributes) {
	<div class="relative">
		<div class={
			"relative",
			getTextFieldContainerClasses(variant, errorText != "", disabled)
		}>
			<select 
				{ attrs... }
				required?={ required }
				disabled?={ disabled }
				class={
					"w-full bg-transparent outline-none transition-standard peer appearance-none cursor-pointer",
					getTextFieldInputClasses(variant)
				}
			>
				for _, option := range options {
					<option value={ option.Value } selected?={ option.Value == selected }>
						{ option.Label }
					</option>
				}
			</select>
			<div class="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
				<span class="material-symbols-outlined text-on-surface-variant">expand_more</span>
			</div>
			if label != "" {
				<label class={
					"absolute left-4 top-2 text-label-small transition-all duration-200 pointer-events-none",
					templ.KV("text-primary", errorText == ""),
					templ.KV("text-error", errorText != ""),
					templ.KV("text-on-surface-variant", disabled)
				}>
					{ label }
					if required {
						<span class="text-error ml-1">*</span>
					}
				</label>
			}
		</div>
		if helperText != "" || errorText != "" {
			<div class="mt-1 px-4">
				if errorText != "" {
					<p class="text-body-small text-error flex items-center">
						<span class="material-symbols-outlined text-sm mr-1">error</span>
						{ errorText }
					</p>
				} else {
					<p class="text-body-small text-on-surface-variant">{ helperText }</p>
				}
			</div>
		}
	</div>
}

// SelectOption represents an option in a select field
type SelectOption struct {
	Value string
	Label string
}

// Helper functions for input styling
func getTextFieldContainerClasses(variant InputVariant, hasError bool, disabled bool) string {
	base := "rounded-extra-small transition-standard"
	
	if disabled {
		base += " opacity-38"
	}
	
	switch variant {
	case InputFilled:
		if hasError {
			return base + " bg-surface-variant border-b-2 border-error hover:bg-surface-container focus-within:bg-surface-container focus-within:border-error"
		}
		return base + " bg-surface-variant border-b border-on-surface-variant hover:bg-surface-container focus-within:bg-surface-container focus-within:border-b-2 focus-within:border-primary"
	case InputOutlined:
		if hasError {
			return base + " border-2 border-error hover:border-error focus-within:border-error"
		}
		return base + " border border-outline hover:border-on-surface focus-within:border-2 focus-within:border-primary"
	default:
		return base + " bg-surface-variant border-b border-on-surface-variant hover:bg-surface-container focus-within:bg-surface-container focus-within:border-b-2 focus-within:border-primary"
	}
}

func getTextFieldInputClasses(variant InputVariant) string {
	switch variant {
	case InputFilled:
		return "pt-6 pb-2 px-4 text-body-large text-on-surface"
	case InputOutlined:
		return "pt-6 pb-2 px-4 text-body-large text-on-surface"
	default:
		return "pt-6 pb-2 px-4 text-body-large text-on-surface"
	}
}
