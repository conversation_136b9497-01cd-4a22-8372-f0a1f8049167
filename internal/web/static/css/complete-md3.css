/* Material Design 3 Complete Implementation */
/* This provides ALL the CSS classes needed for the templates */

:root {
  /* Primary Colors */
  --md-sys-color-primary: #1565c0;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-primary-container: #bbdefb;
  --md-sys-color-on-primary-container: #0d47a1;

  /* Secondary Colors */
  --md-sys-color-secondary: #42a5f5;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-secondary-container: #e3f2fd;
  --md-sys-color-on-secondary-container: #0277bd;

  /* Tertiary Colors */
  --md-sys-color-tertiary: #81c784;
  --md-sys-color-on-tertiary: #ffffff;
  --md-sys-color-tertiary-container: #e8f5e8;
  --md-sys-color-on-tertiary-container: #2e7d32;

  /* Error Colors */
  --md-sys-color-error: #f44336;
  --md-sys-color-on-error: #ffffff;
  --md-sys-color-error-container: #ffebee;
  --md-sys-color-on-error-container: #b71c1c;

  /* Surface Colors */
  --md-sys-color-background: #ffffff;
  --md-sys-color-on-background: #1c1b1f;
  --md-sys-color-surface: #ffffff;
  --md-sys-color-on-surface: #1c1b1f;
  --md-sys-color-surface-variant: #f5f5f5;
  --md-sys-color-on-surface-variant: #49454f;

  /* Surface Container Colors */
  --md-sys-color-surface-container-lowest: #ffffff;
  --md-sys-color-surface-container-low: #fafafa;
  --md-sys-color-surface-container: #f5f5f5;
  --md-sys-color-surface-container-high: #eeeeee;
  --md-sys-color-surface-container-highest: #e0e0e0;

  /* Outline Colors */
  --md-sys-color-outline: #757575;
  --md-sys-color-outline-variant: #bdbdbd;

  /* Other Colors */
  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;

  /* Inverse Colors */
  --md-sys-color-inverse-surface: #323232;
  --md-sys-color-inverse-on-surface: #f5f5f5;
  --md-sys-color-inverse-primary: #90caf9;

  /* Elevation */
  --md-sys-elevation-level1: 0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level2: 0px 1px 2px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level3: 0px 1px 3px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level4: 0px 2px 3px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level5: 0px 4px 4px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);
}

/* Dark Mode */
[data-theme="dark"] {
  --md-sys-color-primary: #90caf9;
  --md-sys-color-on-primary: #0d47a1;
  --md-sys-color-primary-container: #1565c0;
  --md-sys-color-on-primary-container: #e3f2fd;

  --md-sys-color-secondary: #81c784;
  --md-sys-color-on-secondary: #2e7d32;
  --md-sys-color-secondary-container: #4caf50;
  --md-sys-color-on-secondary-container: #e8f5e8;

  --md-sys-color-background: #121212;
  --md-sys-color-on-background: #e0e0e0;
  --md-sys-color-surface: #121212;
  --md-sys-color-on-surface: #e0e0e0;
  --md-sys-color-surface-variant: #424242;
  --md-sys-color-on-surface-variant: #bdbdbd;

  --md-sys-color-surface-container-lowest: #0f0f0f;
  --md-sys-color-surface-container-low: #1e1e1e;
  --md-sys-color-surface-container: #232323;
  --md-sys-color-surface-container-high: #2e2e2e;
  --md-sys-color-surface-container-highest: #333333;

  --md-sys-color-outline: #757575;
  --md-sys-color-outline-variant: #424242;

  --md-sys-color-inverse-surface: #e0e0e0;
  --md-sys-color-inverse-on-surface: #323232;
  --md-sys-color-inverse-primary: #1565c0;
}

/* Base styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

body {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background-color: var(--md-sys-color-background);
  color: var(--md-sys-color-on-background);
  line-height: 1.5;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== BACKGROUND COLORS ===== */
.bg-background { background-color: var(--md-sys-color-background); }
.bg-surface { background-color: var(--md-sys-color-surface); }
.bg-surface-variant { background-color: var(--md-sys-color-surface-variant); }
.bg-surface-container-lowest { background-color: var(--md-sys-color-surface-container-lowest); }
.bg-surface-container-low { background-color: var(--md-sys-color-surface-container-low); }
.bg-surface-container { background-color: var(--md-sys-color-surface-container); }
.bg-surface-container-high { background-color: var(--md-sys-color-surface-container-high); }
.bg-surface-container-highest { background-color: var(--md-sys-color-surface-container-highest); }

.bg-primary { background-color: var(--md-sys-color-primary); }
.bg-primary-container { background-color: var(--md-sys-color-primary-container); }
.bg-secondary { background-color: var(--md-sys-color-secondary); }
.bg-secondary-container { background-color: var(--md-sys-color-secondary-container); }
.bg-tertiary { background-color: var(--md-sys-color-tertiary); }
.bg-tertiary-container { background-color: var(--md-sys-color-tertiary-container); }
.bg-error { background-color: var(--md-sys-color-error); }
.bg-error-container { background-color: var(--md-sys-color-error-container); }

/* ===== TEXT COLORS ===== */
.text-on-background { color: var(--md-sys-color-on-background); }
.text-on-surface { color: var(--md-sys-color-on-surface); }
.text-on-surface-variant { color: var(--md-sys-color-on-surface-variant); }
.text-on-primary { color: var(--md-sys-color-on-primary); }
.text-on-primary-container { color: var(--md-sys-color-on-primary-container); }
.text-on-secondary { color: var(--md-sys-color-on-secondary); }
.text-on-secondary-container { color: var(--md-sys-color-on-secondary-container); }
.text-on-tertiary { color: var(--md-sys-color-on-tertiary); }
.text-on-tertiary-container { color: var(--md-sys-color-on-tertiary-container); }
.text-on-error { color: var(--md-sys-color-on-error); }
.text-on-error-container { color: var(--md-sys-color-on-error-container); }

/* ===== BORDERS ===== */
.border-outline { border-color: var(--md-sys-color-outline); }
.border-outline-variant { border-color: var(--md-sys-color-outline-variant); }

/* ===== MATERIAL DESIGN TYPOGRAPHY ===== */
.text-display-large { font-size: 57px; line-height: 64px; letter-spacing: -0.25px; font-weight: 400; }
.text-display-medium { font-size: 45px; line-height: 52px; font-weight: 400; }
.text-display-small { font-size: 36px; line-height: 44px; font-weight: 400; }
.text-headline-large { font-size: 32px; line-height: 40px; font-weight: 400; }
.text-headline-medium { font-size: 28px; line-height: 36px; font-weight: 400; }
.text-headline-small { font-size: 24px; line-height: 32px; font-weight: 400; }
.text-title-large { font-size: 22px; line-height: 28px; font-weight: 400; }
.text-title-medium { font-size: 16px; line-height: 24px; letter-spacing: 0.15px; font-weight: 500; }
.text-title-small { font-size: 14px; line-height: 20px; letter-spacing: 0.1px; font-weight: 500; }
.text-body-large { font-size: 16px; line-height: 24px; letter-spacing: 0.5px; font-weight: 400; }
.text-body-medium { font-size: 14px; line-height: 20px; letter-spacing: 0.25px; font-weight: 400; }
.text-body-small { font-size: 12px; line-height: 16px; letter-spacing: 0.4px; font-weight: 400; }
.text-label-large { font-size: 14px; line-height: 20px; letter-spacing: 0.1px; font-weight: 500; }
.text-label-medium { font-size: 12px; line-height: 16px; letter-spacing: 0.5px; font-weight: 500; }
.text-label-small { font-size: 11px; line-height: 16px; letter-spacing: 0.5px; font-weight: 500; }

/* ===== MATERIAL DESIGN BORDER RADIUS ===== */
.rounded-none { border-radius: 0; }
.rounded-extra-small { border-radius: 4px; }
.rounded-small { border-radius: 8px; }
.rounded-medium { border-radius: 12px; }
.rounded-large { border-radius: 16px; }
.rounded-extra-large { border-radius: 28px; }
.rounded-full { border-radius: 9999px; }

/* ===== ELEVATION SHADOWS ===== */
.shadow-elevation-0 { box-shadow: none; }
.shadow-elevation-1 { box-shadow: var(--md-sys-elevation-level1); }
.shadow-elevation-2 { box-shadow: var(--md-sys-elevation-level2); }
.shadow-elevation-3 { box-shadow: var(--md-sys-elevation-level3); }
.shadow-elevation-4 { box-shadow: var(--md-sys-elevation-level4); }
.shadow-elevation-5 { box-shadow: var(--md-sys-elevation-level5); }

/* ===== TRANSITIONS ===== */
.transition-standard { transition: all 200ms cubic-bezier(0.2, 0, 0, 1); }
.transition-emphasized { transition: all 500ms cubic-bezier(0.2, 0, 0, 1); }

/* ===== LAYOUT UTILITIES ===== */
.h-full { height: 100%; }
.w-full { width: 100%; }
.min-h-screen { min-height: 100vh; }
.min-w-0 { min-width: 0; }

/* Flexbox */
.flex { display: flex; }
.flex-1 { flex: 1 1 0%; }
.flex-col { flex-direction: column; }
.flex-shrink-0 { flex-shrink: 0; }
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }

/* Grid */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* ===== SPACING ===== */
.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }

.pt-0 { padding-top: 0; }
.pt-4 { padding-top: 1rem; }
.pb-0 { padding-bottom: 0; }

.m-0 { margin: 0; }
.m-2 { margin: 0.5rem; }
.m-4 { margin: 1rem; }

.mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }
.my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-8 { margin-bottom: 2rem; }

.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }

.mr-2 { margin-right: 0.5rem; }

/* ===== GAPS ===== */
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }

.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

/* ===== SIZING ===== */
.w-2 { width: 0.5rem; }
.w-3 { width: 0.75rem; }
.w-8 { width: 2rem; }
.w-12 { width: 3rem; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.w-64 { width: 16rem; }

.h-2 { height: 0.5rem; }
.h-3 { height: 0.75rem; }
.h-8 { height: 2rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }

/* ===== POSITIONING ===== */
.fixed { position: fixed; }
.relative { position: relative; }
.absolute { position: absolute; }

.bottom-6 { bottom: 1.5rem; }
.right-6 { right: 1.5rem; }

/* ===== BORDERS ===== */
.border { border-width: 1px; }
.border-t { border-top-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-r { border-right-width: 1px; }

/* ===== OVERFLOW ===== */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* ===== FONT UTILITIES ===== */
.font-sans { font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== CURSORS ===== */
.cursor-pointer { cursor: pointer; }

/* ===== VISIBILITY ===== */
.hidden { display: none; }

/* ===== RESPONSIVE UTILITIES ===== */
@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:col-span-2 { grid-column: span 2 / span 2; }
  .lg\:w-64 { width: 16rem; }
  .lg\:block { display: block; }
  .lg\:justify-start { justify-content: flex-start; }
}

/* ===== HOVER EFFECTS ===== */
.hover\:bg-surface-variant:hover { background-color: var(--md-sys-color-surface-variant); }
.hover\:shadow-elevation-1:hover { box-shadow: var(--md-sys-elevation-level1); }
.hover\:shadow-elevation-2:hover { box-shadow: var(--md-sys-elevation-level2); }

/* ===== FOCUS STYLES ===== */
.focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible:focus-visible {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

/* ===== MATERIAL DESIGN SPECIFIC CLASSES ===== */

/* Navigation Rail specific styles */
.w-20 { width: 5rem; }

/* Icon styles */
.material-symbols-outlined {
  font-family: 'Material Symbols Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }

/* Additional specific size utilities needed */
.w-48 { width: 12rem; }
.w-56 { width: 14rem; }
.w-72 { width: 18rem; }
.w-80 { width: 20rem; }
.w-96 { width: 24rem; }