/* Material Design 3 Design System for GoAssistant */

/* Material Design 3 Color Tokens - Blue & White Theme */
/* Light Mode */
:root {
  /* Primary Colors - Rich Blue */
  --md-sys-color-primary: #0061A4;
  --md-sys-color-on-primary: #FFFFFF;
  --md-sys-color-primary-container: #D1E4FF;
  --md-sys-color-on-primary-container: #001D36;

  /* Secondary Colors - Blue Grey */
  --md-sys-color-secondary: #535F70;
  --md-sys-color-on-secondary: #FFFFFF;
  --md-sys-color-secondary-container: #D7E3F7;
  --md-sys-color-on-secondary-container: #101C2B;

  /* Tertiary Colors - Accent Gold */
  --md-sys-color-tertiary: #6B5D00;
  --md-sys-color-on-tertiary: #FFFFFF;
  --md-sys-color-tertiary-container: #F7E087;
  --md-sys-color-on-tertiary-container: #201C00;

  /* Error Colors */
  --md-sys-color-error: #BA1A1A;
  --md-sys-color-on-error: #FFFFFF;
  --md-sys-color-error-container: #FFDAD6;
  --md-sys-color-on-error-container: #410002;

  /* Surface Colors - Pure White Background */
  --md-sys-color-background: #FDFCFF;
  --md-sys-color-on-background: #1A1C1E;
  --md-sys-color-surface: #FDFCFF;
  --md-sys-color-on-surface: #1A1C1E;
  --md-sys-color-surface-variant: #DFE2EB;
  --md-sys-color-on-surface-variant: #43474E;
  --md-sys-color-outline: #73777F;
  --md-sys-color-outline-variant: #C3C7CF;

  /* Surface Containers */
  --md-sys-color-surface-container-lowest: #FFFFFF;
  --md-sys-color-surface-container-low: #F4F6FA;
  --md-sys-color-surface-container: #EEF1F5;
  --md-sys-color-surface-container-high: #E8EBEF;
  --md-sys-color-surface-container-highest: #E2E5E9;

  /* Inverse Colors */
  --md-sys-color-inverse-surface: #2E3135;
  --md-sys-color-inverse-on-surface: #F0F2F6;
  --md-sys-color-inverse-primary: #9ECAFF;

  /* Shadow and Scrim */
  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;
}

/* Dark Mode Colors */
[data-theme="dark"] {
  /* Primary Colors - Light Blue */
  --md-sys-color-primary: #9ECAFF;
  --md-sys-color-on-primary: #003258;
  --md-sys-color-primary-container: #00497D;
  --md-sys-color-on-primary-container: #D1E4FF;

  /* Secondary Colors */
  --md-sys-color-secondary: #BBD9F0;
  --md-sys-color-on-secondary: #253140;
  --md-sys-color-secondary-container: #3C4857;
  --md-sys-color-on-secondary-container: #D7E3F7;

  /* Tertiary Colors */
  --md-sys-color-tertiary: #DBC961;
  --md-sys-color-on-tertiary: #373100;
  --md-sys-color-tertiary-container: #504600;
  --md-sys-color-on-tertiary-container: #F7E087;

  /* Error Colors */
  --md-sys-color-error: #FFB4AB;
  --md-sys-color-on-error: #690005;
  --md-sys-color-error-container: #93000A;
  --md-sys-color-on-error-container: #FFDAD6;

  /* Surface Colors - Dark Background */
  --md-sys-color-background: #1A1C1E;
  --md-sys-color-on-background: #E2E2E6;
  --md-sys-color-surface: #1A1C1E;
  --md-sys-color-on-surface: #E2E2E6;
  --md-sys-color-surface-variant: #43474E;
  --md-sys-color-on-surface-variant: #C3C7CF;
  --md-sys-color-outline: #8D9199;
  --md-sys-color-outline-variant: #43474E;

  /* Surface Containers */
  --md-sys-color-surface-container-lowest: #0F1113;
  --md-sys-color-surface-container-low: #191C20;
  --md-sys-color-surface-container: #1D2024;
  --md-sys-color-surface-container-high: #272A2F;
  --md-sys-color-surface-container-highest: #32353A;

  /* Elevation Tints for Dark Mode */
  --md-sys-color-surface-tint: #9ECAFF;
  --md-sys-elevation-level1: #222529;
  --md-sys-elevation-level2: #272A2E;
  --md-sys-elevation-level3: #2C2F33;
  --md-sys-elevation-level4: #2E3135;
  --md-sys-elevation-level5: #313539;

  /* Inverse Colors */
  --md-sys-color-inverse-surface: #E2E2E6;
  --md-sys-color-inverse-on-surface: #2E3135;
  --md-sys-color-inverse-primary: #0061A4;
}

/* Typography Scale */
.text-display-large {
  font-size: 57px;
  line-height: 64px;
  font-weight: 400;
  letter-spacing: -0.25px;
}

.text-display-medium {
  font-size: 45px;
  line-height: 52px;
  font-weight: 400;
}

.text-display-small {
  font-size: 36px;
  line-height: 44px;
  font-weight: 400;
}

.text-headline-large {
  font-size: 32px;
  line-height: 40px;
  font-weight: 400;
}

.text-headline-medium {
  font-size: 28px;
  line-height: 36px;
  font-weight: 400;
}

.text-headline-small {
  font-size: 24px;
  line-height: 32px;
  font-weight: 400;
}

.text-title-large {
  font-size: 22px;
  line-height: 28px;
  font-weight: 400;
}

.text-title-medium {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  letter-spacing: 0.15px;
}

.text-title-small {
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  letter-spacing: 0.1px;
}

.text-body-large {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  letter-spacing: 0.5px;
}

.text-body-medium {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  letter-spacing: 0.25px;
}

.text-body-small {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  letter-spacing: 0.4px;
}

.text-label-large {
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  letter-spacing: 0.1px;
}

.text-label-medium {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.text-label-small {
  font-size: 11px;
  line-height: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Material Design 3 Elevation */
.elevation-0 { box-shadow: none; }
.elevation-1 { box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15); }
.elevation-2 { box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15); }
.elevation-3 { box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15); }
.elevation-4 { box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15); }
.elevation-5 { box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15); }

/* Material Design 3 Shape */
.shape-none { border-radius: 0; }
.shape-extra-small { border-radius: 4px; }
.shape-small { border-radius: 8px; }
.shape-medium { border-radius: 12px; }
.shape-large { border-radius: 16px; }
.shape-extra-large { border-radius: 28px; }
.shape-full { border-radius: 9999px; }

/* Font Families for Internationalization */
:root {
  /* Latin */
  --font-family-base: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  /* Traditional Chinese - Using Noto Sans TC for better readability */
  --font-family-zh-tw: 'Noto Sans TC', 'Microsoft JhengHei', 'PingFang TC', sans-serif;
}

/* Language-specific typography adjustments */
[lang="zh-TW"] {
  /* Slightly larger font size for better readability */
  --body-font-size: 17px;
  --label-font-size: 15px;
  /* Adjusted line height */
  --line-height-base: 1.7;
}

[lang="en"] {
  --body-font-size: 16px;
  --label-font-size: 14px;
  --line-height-base: 1.5;
}

/* Base Styles */
body {
  font-family: var(--font-family-base), var(--font-family-zh-tw);
  background-color: var(--md-sys-color-background);
  color: var(--md-sys-color-on-background);
  margin: 0;
  padding: 0;
  line-height: var(--line-height-base, 1.5);
}

/* Focus Visible */
.focus-visible:focus {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

/* Transitions */
.transition-standard {
  transition: all 200ms cubic-bezier(0.2, 0, 0, 1);
}

.transition-emphasized {
  transition: all 500ms cubic-bezier(0.2, 0, 0, 1);
}

/* HTMX Loading States */
.htmx-request {
  opacity: 0.6;
  transition: opacity 200ms ease-in-out;
}

.htmx-indicator {
  display: none;
}

.htmx-request .htmx-indicator {
  display: block;
}

/* Dark Mode Elevation System */
[data-theme="dark"] .elevation-1 { background-color: var(--md-sys-elevation-level1); }
[data-theme="dark"] .elevation-2 { background-color: var(--md-sys-elevation-level2); }
[data-theme="dark"] .elevation-3 { background-color: var(--md-sys-elevation-level3); }
[data-theme="dark"] .elevation-4 { background-color: var(--md-sys-elevation-level4); }
[data-theme="dark"] .elevation-5 { background-color: var(--md-sys-elevation-level5); }

/* Remove shadows in dark mode */
[data-theme="dark"] .shadow-sm,
[data-theme="dark"] .shadow-md,
[data-theme="dark"] .shadow-lg,
[data-theme="dark"] .shadow-xl,
[data-theme="dark"] .elevation-1,
[data-theme="dark"] .elevation-2,
[data-theme="dark"] .elevation-3,
[data-theme="dark"] .elevation-4,
[data-theme="dark"] .elevation-5 {
  box-shadow: none;
}

/* Responsive Grid System */
.grid-8dp {
  display: grid;
  gap: 8px;
}

.grid-16dp {
  display: grid;
  gap: 16px;
}

.grid-24dp {
  display: grid;
  gap: 24px;
}
