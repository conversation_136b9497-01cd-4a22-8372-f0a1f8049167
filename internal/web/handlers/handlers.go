package handlers

import (
	"log/slog"
	"net/http"
	"time"

	"github.com/koopa0/assistant-go/internal/assistant"
	"github.com/koopa0/assistant-go/internal/web/i18n"
	"github.com/koopa0/assistant-go/internal/web/templates"
)

// Handlers contains all HTTP handlers for the web interface
type Handlers struct {
	assistant *assistant.Assistant
	logger    *slog.Logger
}

// New creates a new Handlers instance
func New(assistant *assistant.Assistant, logger *slog.Logger) *Handlers {
	return &Handlers{
		assistant: assistant,
		logger:    logger,
	}
}

// getLanguageFromRequest extracts the language preference from the request
func (h *Handlers) getLanguageFromRequest(r *http.Request) string {
	// Check cookie first
	if cookie, err := r.<PERSON>("lang"); err == nil {
		if _, exists := i18n.SupportedLanguages[cookie.Value]; exists {
			return cookie.Value
		}
	}

	// Check Accept-Language header
	acceptLang := r.Header.Get("Accept-Language")
	if len(acceptLang) >= 2 {
		lang := acceptLang[:2]
		if _, exists := i18n.SupportedLanguages[lang]; exists {
			return lang
		}
		// Check for zh-TW specifically
		if len(acceptLang) >= 5 && acceptLang[:5] == "zh-TW" {
			return "zh-TW"
		}
	}

	// Default to English
	return "en"
}

// getThemeFromRequest extracts the theme preference from the request
func (h *Handlers) getThemeFromRequest(r *http.Request) string {
	if cookie, err := r.Cookie("theme"); err == nil {
		if cookie.Value == "dark" || cookie.Value == "light" {
			return cookie.Value
		}
	}
	return "light" // Default theme
}

// HandleDashboard renders the dashboard page
func (h *Handlers) HandleDashboard(w http.ResponseWriter, r *http.Request) {
	lang := h.getLanguageFromRequest(r)
	theme := h.getThemeFromRequest(r)

	// Get dashboard data
	dashboardData := templates.DashboardData{
		Lang:           lang,
		ActiveAgents:   4,
		TasksCompleted: 127,
		SystemHealth:   "98%",
		Uptime:         time.Hour * 24 * 7,
		AgentStatuses: []templates.AgentStatus{
			{
				Name:        i18n.T("nav.development", lang),
				Description: "Go development tools and analysis",
				Status:      i18n.T("chat.agent_online", lang),
				IsActive:    true,
				LastUsed:    time.Now().Add(-time.Hour * 2),
				Href:        "/development",
			},
			{
				Name:        i18n.T("nav.database", lang),
				Description: "PostgreSQL management and queries",
				Status:      i18n.T("chat.agent_online", lang),
				IsActive:    true,
				LastUsed:    time.Now().Add(-time.Minute * 30),
				Href:        "/database",
			},
			{
				Name:        i18n.T("nav.infrastructure", lang),
				Description: "Kubernetes and Docker monitoring",
				Status:      i18n.T("chat.agent_offline", lang),
				IsActive:    false,
				LastUsed:    time.Now().Add(-time.Hour * 24),
				Href:        "/infrastructure",
			},
			{
				Name:        "Research Agent",
				Description: "AI-powered search and analysis",
				Status:      i18n.T("chat.agent_online", lang),
				IsActive:    true,
				LastUsed:    time.Now().Add(-time.Minute * 15),
				Href:        "/research",
			},
		},
		RecentActivities: []templates.Activity{
			{
				ID:          "1",
				Type:        "chat",
				Description: "New conversation started with Development Agent",
				Timestamp:   time.Now().Add(-time.Minute * 5),
				Icon:        "chat",
				Status:      "success",
			},
			{
				ID:          "2",
				Type:        "database",
				Description: "Database query executed successfully",
				Timestamp:   time.Now().Add(-time.Minute * 15),
				Icon:        "storage",
				Status:      "success",
			},
			{
				ID:          "3",
				Type:        "system",
				Description: "System health check completed",
				Timestamp:   time.Now().Add(-time.Minute * 30),
				Icon:        "health_and_safety",
				Status:      "success",
			},
		},
		RecentChats: []templates.ChatSummary{
			{
				ID:        "chat-1",
				Title:     "Go Code Review",
				Agent:     "Development Agent",
				Timestamp: time.Now().Add(-time.Hour * 2),
				Preview:   "Reviewing the new authentication middleware implementation...",
			},
			{
				ID:        "chat-2",
				Title:     "Database Optimization",
				Agent:     "Database Agent",
				Timestamp: time.Now().Add(-time.Hour * 4),
				Preview:   "Analyzing query performance and suggesting indexes...",
			},
		},
	}

	layoutData := templates.AppLayoutData{
		BaseLayoutData: templates.BaseLayoutData{
			Title:       i18n.T("nav.home", lang),
			Description: "GoAssistant Dashboard - AI-powered development tools",
			Lang:        lang,
			Theme:       theme,
		},
		CurrentPage: "dashboard",
	}

	component := templates.AppLayout(layoutData, templates.EnhancedDashboard(dashboardData))

	if err := component.Render(r.Context(), w); err != nil {
		h.logger.Error("Failed to render dashboard", slog.Any("error", err))
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// HandleChat renders the chat interface
func (h *Handlers) HandleChat(w http.ResponseWriter, r *http.Request) {
	lang := h.getLanguageFromRequest(r)
	theme := h.getThemeFromRequest(r)

	layoutData := templates.AppLayoutData{
		BaseLayoutData: templates.BaseLayoutData{
			Title:       i18n.T("nav.chat", lang),
			Description: "Chat with AI assistants",
			Lang:        lang,
			Theme:       theme,
		},
		CurrentPage: "chat",
	}

	component := templates.AppLayout(layoutData, templates.ModernChatPage(templates.ChatPageData{
		Messages:        []templates.Message{},
		ActiveAgent:     templates.Agent{ID: "development", Name: "Development Assistant", Description: "AI助手專門協助Go開發工作", Status: "online"},
		AvailableAgents: []templates.Agent{
			{ID: "development", Name: "Development Assistant", Description: "AI助手專門協助Go開發工作", Status: "online"},
			{ID: "database", Name: "Database Expert", Description: "資料庫設計與優化專家", Status: "online"},
			{ID: "infrastructure", Name: "DevOps Engineer", Description: "基礎設施管理與部署助手", Status: "online"},
		},
		Lang: lang,
	}))

	if err := component.Render(r.Context(), w); err != nil {
		h.logger.Error("Failed to render chat", slog.Any("error", err))
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// HandleAPI handles API endpoints for HTMX requests
func (h *Handlers) HandleAPI(w http.ResponseWriter, r *http.Request) {
	// Set HTMX headers
	w.Header().Set("Content-Type", "text/html")

	switch r.URL.Path {
	case "/api/activities":
		h.handleActivitiesAPI(w, r)
	case "/api/stats":
		h.handleStatsAPI(w, r)
	default:
		http.NotFound(w, r)
	}
}

// handleActivitiesAPI returns recent activities as HTML
func (h *Handlers) handleActivitiesAPI(w http.ResponseWriter, r *http.Request) {
	lang := h.getLanguageFromRequest(r)

	activities := []templates.Activity{
		{
			ID:          "1",
			Type:        "chat",
			Description: "New conversation started",
			Timestamp:   time.Now().Add(-time.Minute * 2),
			Icon:        "chat",
			Status:      "success",
		},
		{
			ID:          "2",
			Type:        "system",
			Description: "Background task completed",
			Timestamp:   time.Now().Add(-time.Minute * 5),
			Icon:        "task_alt",
			Status:      "success",
		},
	}

	for _, activity := range activities {
		component := templates.ActivityItem(activity, lang)
		if err := component.Render(r.Context(), w); err != nil {
			h.logger.Error("Failed to render activity", slog.Any("error", err))
			return
		}
	}
}

// handleStatsAPI returns updated statistics
func (h *Handlers) handleStatsAPI(w http.ResponseWriter, r *http.Request) {
	// Return updated stats as JSON or HTML depending on request
	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status": "ok", "timestamp": "` + time.Now().Format(time.RFC3339) + `"}`))
}

// HandlePreferences handles user preference updates
func (h *Handlers) HandlePreferences(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	switch r.URL.Path {
	case "/api/preferences/theme":
		h.handleThemePreference(w, r)
	case "/api/preferences/language":
		h.handleLanguagePreference(w, r)
	default:
		http.NotFound(w, r)
	}
}

// handleThemePreference updates theme preference
func (h *Handlers) handleThemePreference(w http.ResponseWriter, r *http.Request) {
	// Parse theme from request body
	// Set cookie and respond
	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status": "ok"}`))
}

// handleLanguagePreference updates language preference
func (h *Handlers) handleLanguagePreference(w http.ResponseWriter, r *http.Request) {
	// Parse language from request body
	// Set cookie and respond
	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status": "ok"}`))
}
